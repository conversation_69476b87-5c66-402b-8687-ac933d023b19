---
mode: agent
name: Base Prompt
---
# 🛠️ GitHub Copilot Guidelines for SvelteKit + Appwrite Chat PWA

## 📘 Project Overview

Building a **Progressive Web App (PWA)** chat application using:

- typescript
- **Frontend:** [SvelteKit](https://kit.svelte.dev/)
- **UI Framework**: [Shadcn](https://ui.shadcn.com/)
- **Icons:** [SimpleIcons](https://simpleicons.org/)
- **Animations:** [Animate UI ](https://animate-ui.com/)
- **Styling:** [Tailwind CSS](https://tailwindcss.com/)
- **State Management:** Svelte stores
- **Real-time Database:** [Appwrite](https://appwrite.io/)
- **Authentication:** Appwrite's built-in user management
- **Deployment:** Vercel or Netlify (optional)
- **Backend:** [Appwrite](https://appwrite.io/) as BaaS (Backend-as-a-Service)

---

## 🔐 User Authentication

- **Email/Password Authentication**
  - Enable registration & login via Appwrite's `Account.createEmailSession()`
- **Email Verification Flow**
  - Use Appwrite’s `Account.createVerification()` and `Account.updateVerification()`
- **User Profile Structure**
  - `email` (unique, required)  
  - `displayName` (non-unique, searchable)  
  - `avatarUrl` (optional image URL)

---

## 📱 PWA Capabilities

- **Offline Support**
  - Implement `service-worker.js` with caching strategies.
- **Installable Experience**
  - Define `manifest.json` with proper metadata (name, icons, display mode, etc.)

---

## 👥 Chat & Friend Management

- **Friend Discovery**
  - Search by **email** using `Query.equal()`
  - Search by **displayName** using `Query.search()` (ensure full-text index)
- **Friend Request Flow**
  - Create friend request documents in a `FriendRequests` collection.
  - Support send/accept/reject operations.
- **No Public Friend List**
  - Only mutual connections are visible and messageable.

---

## 💬 Chat Rooms

- **Types**
  - 1-to-1 chat
  - Group chat
- **Room Access**
  - All rooms are **password-protected**, even by the creator.
  - Password must be validated via Appwrite **Functions** (server-side only)
- **Access Control**
  - Users must be explicitly granted read/write access.
  - Permissions handled **only** via Appwrite Server SDK (Functions)

---

## ⚡ Real-time Messaging

- Use `client.subscribe()` to subscribe to `Messages` collection in real-time.
- Append incoming messages to local chat using Svelte stores.

---

## 📦 Appwrite Usage & Best Practices

### SDK Usage

Import and use:

```js
import { Client, Account, Databases, Storage } from 'appwrite';
```

Configure:

```bash
VITE_APPWRITE_ENDPOINT=<your-endpoint>
VITE_APPWRITE_PROJECT_ID=<your-project-id>
```

### Database Structure

- **Users Collection**
- **ChatRooms Collection**
- **FriendRequests Collection**
- **Messages Collection**  
  ➤ All chat messages stored in one shared collection for performance & scalability

### Permissions (Critical)

> ❗ All permission handling MUST be done server-side via Appwrite Functions

- **Client SDK must NOT set or modify permissions**
- Use:
  - `Permission.read(Role.user(USER_ID))`
  - `Permission.read(Role.team(TEAM_ID))`
- Use Appwrite Functions to:
  - Validate room passwords
  - Assign/remove document permissions dynamically

### Queries

- Exact match: `Query.equal('email', userEmail)`
- Full-text search: `Query.search('displayName', 'john')`

Ensure indexing is configured properly in Appwrite Console.

---

## 🧩 SvelteKit Project Structure

### 📁 State Management

- Create Svelte stores (e.g., `src/lib/stores/auth.js`) for:
  - Authenticated user
  - User profile
  - Chat messages

### 🧭 Routing

- Use **file-based routing**
  - Example: `src/routes/chat/[roomId]/+page.svelte`

### 🎨 Layouts & Route Guards

- Global layout: `+layout.svelte`
- Add route protection logic using `load` functions and session checks

---

## ✅ Summary Checklist

| Feature                     | Implemented With              | Notes                                                   |
|----------------------------|-------------------------------|----------------------------------------------------------|
| Auth & Verification        | Appwrite `Account`            | Email-based auth, must verify before proceeding          |
| Real-time Messaging        | Appwrite `client.subscribe()` | Listen to `Messages` updates                            |
| Room Permissions           | Appwrite **Functions**        | Never allow client to update sensitive access rules     |
| PWA Support                | `manifest.json`, SW           | Make it installable & offline-ready                     |
| Friend Request System      | Custom collection              | Use `FriendRequests` with status field                  |
| Chat Room Access           | Server-side validation         | Password logic secured via Appwrite Function            |
