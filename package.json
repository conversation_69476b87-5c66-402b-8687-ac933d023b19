{"name": "starter-for-svelte", "version": "0.0.1", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./jsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./jsconfig.json --watch"}, "devDependencies": {"@sveltejs/adapter-auto": "^4.0.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tsconfig/svelte": "^5.0.4", "@types/node": "^24.0.10", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "prettier": "^3.5.3", "prettier-plugin-svelte": "^3.3.3", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^4.1.3", "typescript": "^5.5.3", "vite": "^6.0.0"}, "type": "module", "dependencies": {"@appwrite.io/pink-icons": "^0.25.0", "@lucide/svelte": "^0.525.0", "@sveltejs/adapter-node": "^5.2.12", "@tailwindcss/vite": "^4.1.3", "animate.css": "^4.1.1", "appwrite": "^16.1.0", "clsx": "^2.1.1", "simple-icons": "^15.4.0", "tailwind-merge": "^3.3.1", "tailwind-variants": "^1.0.0", "tw-animate-css": "^1.3.5", "zod": "^3.25.74"}}