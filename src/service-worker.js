/// <reference types="@sveltejs/kit" />
import { build, files, version } from '$service-worker';

const CACHE_NAME = `cache-${version}`;

const ASSETS = [
    ...build, // SvelteKit-generated build assets
    ...files  // Static assets (from the 'static' directory)
];

// On install, cache all assets
self.addEventListener('install', (event) => {
    event.waitUntil(
        caches.open(CACHE_NAME).then((cache) => {
            console.log('Service Worker: Caching assets...');
            return cache.addAll(ASSETS);
        })
    );
});

// On activate, delete old caches
self.addEventListener('activate', (event) => {
    event.waitUntil(
        caches.keys().then(async (keys) => {
            console.log('Service Worker: Activating and clearing old caches...');
            // delete old caches
            for (const key of keys) {
                if (key !== CACHE_NAME) {
                    await caches.delete(key);
                }
            }
            self.clients.claim(); // Immediately take control of clients
        })
    );
});

// On fetch, serve from cache falling back to network
self.addEventListener('fetch', (event) => {
    // We don't want to cache requests for chrome extensions or non-HTTP/S requests
    if (event.request.url.startsWith('http')) {
        const url = new URL(event.request.url);

        // Only cache GET requests
        if (event.request.method !== 'GET') {
            return;
        }

        // Don't cache range requests
        if (event.request.headers.has('range')) {
            return;
        }

        // Cache-first strategy for assets
        if (ASSETS.includes(url.pathname)) {
            event.respondWith(caches.match(event.request));
            return;
        }

        // Network-first for everything else (e.g., API calls, navigation)
        event.respondWith(
            caches.open(CACHE_NAME).then(async (cache) => {
                try {
                    const response = await fetch(event.request);
                    // Don't cache opaque responses or those that indicate errors
                    if (response.ok || response.type === 'opaque') {
                        cache.put(event.request, response.clone());
                    }
                    return response;
                } catch (error) {
                    const cachedResponse = await cache.match(event.request);
                    if (cachedResponse) {
                        return cachedResponse;
                    }
                    // If both network and cache fail, show a fallback page/message
                    // For now, we'll just re-throw the error or return an error response
                    console.error('Service Worker: Fetch failed and no cache match for', event.request.url, error);
                    return new Response('Offline: Could not retrieve resource', {
                        status: 503,
                        headers: { 'Content-Type': 'text/plain' }
                    });
                }
            })
        );
    }
});
