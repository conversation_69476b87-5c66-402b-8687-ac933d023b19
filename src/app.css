@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

summary::-webkit-details-marker {
  display: none;
}

.checker-background::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-image:
    linear-gradient(#e6e6e690 1px, transparent 1px),
    linear-gradient(90deg, #e6e6e690 1px, transparent 1px);
  background-size: 3.7em 3.7em;
  mask-image: radial-gradient(ellipse at 50% 40%, black 0%, transparent 55%);
  z-index: -1;
  background-position-x: center;
}

/* Custom animations for chat */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.3s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

/* Smooth scrolling for messages */
.messages-container {
  scroll-behavior: smooth;
}

/* Custom scrollbar for webkit browsers */
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Mobile-specific styles */
@media (max-width: 768px) {
  .chat-container {
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile */
  }
}

/* Focus styles for better accessibility */
.focus-ring:focus {
  outline: 2px solid #000;
  outline-offset: 2px;
}
