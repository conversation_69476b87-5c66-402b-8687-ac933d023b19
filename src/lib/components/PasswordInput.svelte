<script lang="ts">
  import { validatePassword, getPasswordStrengthColor, getPasswordStrengthText } from '$lib/utils/passwordValidation';
  
  export let value: string = '';
  export let placeholder: string = 'Mật khẩu';
  export let required: boolean = true;
  export let showStrength: boolean = false;
  export let helperText: string = '';
  export let id: string = '';
  export let className: string = 'w-full border border-gray-300 rounded-lg px-4 py-2 pr-12 focus:outline-none focus:ring-2 focus:ring-black transition bg-white text-black';
  
  let showPassword = false;
  let passwordStrength: 'weak' | 'medium' | 'strong' = 'weak';
  let showPasswordStrength = false;
  
  function togglePasswordVisibility() {
    showPassword = !showPassword;
  }
  
  function handlePasswordChange() {
    if (showStrength && value.length > 0) {
      const validation = validatePassword(value);
      passwordStrength = validation.strength;
      showPasswordStrength = true;
    } else {
      showPasswordStrength = false;
    }
  }
  
  // Reactive statement to update parent component
  $: if (value !== undefined) {
    handlePasswordChange();
  }
</script>

<div class="relative">
  <input
    {id}
    type={showPassword ? 'text' : 'password'}
    bind:value
    on:input={handlePasswordChange}
    on:input
    class={className}
    {placeholder}
    {required}
    autocomplete="current-password"
  />
  
  <!-- Toggle password visibility button -->
  <button
    type="button"
    class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors focus:outline-none focus:text-gray-600"
    on:click={togglePasswordVisibility}
    aria-label={showPassword ? 'Ẩn mật khẩu' : 'Hiện mật khẩu'}
  >
    {#if showPassword}
      <!-- Eye slash icon (hide) -->
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
      </svg>
    {:else}
      <!-- Eye icon (show) -->
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
      </svg>
    {/if}
  </button>
</div>

<!-- Helper text and strength indicator -->
{#if helperText || (showStrength && showPasswordStrength)}
  <div class="flex justify-between items-center mt-1">
    {#if helperText}
      <p class="text-xs text-gray-500">{helperText}</p>
    {/if}
    {#if showStrength && showPasswordStrength}
      <span class="text-xs font-medium {getPasswordStrengthColor(passwordStrength)}">
        {getPasswordStrengthText(passwordStrength)}
      </span>
    {/if}
  </div>
{/if}
