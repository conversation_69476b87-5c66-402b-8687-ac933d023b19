import { functions } from '$lib/appwrite';

// Function IDs - these should match the function IDs in Appwrite Console
const VALIDATE_ROOM_PASSWORD_FUNCTION_ID = 'validate-room-password';
const GRANT_MESSAGE_PERMISSIONS_FUNCTION_ID = 'grant-message-permissions';

export interface RoomValidationResult {
  success: boolean;
  error?: string;
  room?: any;
}

/**
 * Validate room password using Appwrite Function
 * This ensures password validation happens server-side for security
 */
export async function validateRoomPassword(
  roomId: string, 
  password: string, 
  userId: string
): Promise<RoomValidationResult> {
  try {
    const execution = await functions.createExecution(
      VALIDATE_ROOM_PASSWORD_FUNCTION_ID,
      JSON.stringify({ roomId, password, userId }),
      false, // async = false for immediate response
      '/', // path
      'POST' // method
    );

    // Parse the response
    const response = JSON.parse(execution.responseBody);
    return response;
  } catch (err: any) {
    console.error('Room password validation failed:', err);
    return {
      success: false,
      error: err.message || 'Function execution failed'
    };
  }
}

/**
 * Fallback method for when Functions are not available
 * This is less secure but allows the app to work during development
 */
export async function validateRoomPasswordFallback(
  roomId: string,
  password: string,
  databases: any,
  databaseId: string,
  collectionId: string
): Promise<RoomValidationResult> {
  try {
    console.warn('Using fallback password validation - not recommended for production');
    
    // Get room details to verify password
    const room = await databases.getDocument(databaseId, collectionId, roomId);
    
    if (room.password !== password) {
      return {
        success: false,
        error: 'Invalid password'
      };
    }

    return {
      success: true,
      room
    };
  } catch (err: any) {
    console.error('Fallback password validation failed:', err);
    return {
      success: false,
      error: err.message || 'Password validation failed'
    };
  }
}

/**
 * Check if Appwrite Functions are available
 */
export async function checkFunctionsAvailability(): Promise<boolean> {
  try {
    // Try to list functions to see if Functions service is available
    await functions.list();
    return true;
  } catch (err) {
    console.warn('Appwrite Functions not available:', err);
    return false;
  }
}

/**
 * Grant message permissions using Appwrite Function
 * This should be called automatically via database triggers in production
 */
export async function grantMessagePermissions(
  messageId: string,
  roomId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const execution = await functions.createExecution(
      GRANT_MESSAGE_PERMISSIONS_FUNCTION_ID,
      JSON.stringify({ messageId, roomId }),
      false,
      '/',
      'POST'
    );

    const response = JSON.parse(execution.responseBody);
    return response;
  } catch (err: any) {
    console.error('Grant message permissions failed:', err);
    return {
      success: false,
      error: err.message || 'Function execution failed'
    };
  }
}

/**
 * Create room with proper permissions using server-side function
 */
export async function createRoomSecure(
  roomData: any,
  userId: string
): Promise<{ success: boolean; room?: any; error?: string }> {
  try {
    // In a full implementation, this would call a server-side function
    // For now, we'll use a placeholder
    console.warn('createRoomSecure not fully implemented - using client-side creation');
    
    return {
      success: false,
      error: 'Server-side room creation not implemented yet'
    };
  } catch (err: any) {
    console.error('Secure room creation failed:', err);
    return {
      success: false,
      error: err.message || 'Room creation failed'
    };
  }
}
