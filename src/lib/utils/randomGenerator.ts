// Random password and display name generator

// Vietnamese first names
const vietnameseFirstNames = [
  '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>ơ<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>'
];

// Vietnamese last names
const vietnameseLastNames = [
  '<PERSON>uyễn', '<PERSON>rần', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Đặng',
  '<PERSON><PERSON><PERSON>', 'Đỗ', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>'
];

// Adjectives for display names
const adjectives = [
  '<PERSON>', '<PERSON>', 'Brave', 'Swift', '<PERSON>', '<PERSON>', '<PERSON>', 'Strong', 'Wise', 'Kind',
  '<PERSON>', '<PERSON>', '<PERSON>m', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', 'Sun', '<PERSON>', '<PERSON>', '<PERSON>', 'Wind', '<PERSON>', '<PERSON>', 'Gold', 'Silver'
];

// Animals for display names
const animals = [
  'Tiger', 'Dragon', 'Eagle', 'Wolf', 'Lion', 'Bear', 'Fox', 'Hawk', 'Shark', 'Panther',
  'Falcon', 'Leopard', 'Jaguar', 'Phoenix', 'Raven', 'Cobra', 'Viper', 'Lynx', 'Puma', 'Cheetah'
];

// Password character sets
const lowercase = 'abcdefghijklmnopqrstuvwxyz';
const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
const numbers = '0123456789';
const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

// Common weak passwords to avoid
const weakPasswords = [
  'password', '123456', '123456789', 'qwerty', 'abc123', 'password123',
  'admin', 'letmein', 'welcome', 'monkey', 'dragon', 'master', 'shadow',
  'superman', 'michael', 'football', 'baseball', 'liverpool', 'jordan'
];

/**
 * Generate a random Vietnamese display name
 */
export function generateRandomDisplayName(): string {
  const formats = [
    // Vietnamese name format
    () => {
      const lastName = vietnameseLastNames[Math.floor(Math.random() * vietnameseLastNames.length)];
      const firstName = vietnameseFirstNames[Math.floor(Math.random() * vietnameseFirstNames.length)];
      return `${lastName} ${firstName}`;
    },
    // English adjective + animal format
    () => {
      const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
      const animal = animals[Math.floor(Math.random() * animals.length)];
      return `${adjective} ${animal}`;
    },
    // Vietnamese first name + number
    () => {
      const firstName = vietnameseFirstNames[Math.floor(Math.random() * vietnameseFirstNames.length)];
      const number = Math.floor(Math.random() * 999) + 1;
      return `${firstName}${number}`;
    },
    // Adjective + Vietnamese first name
    () => {
      const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
      const firstName = vietnameseFirstNames[Math.floor(Math.random() * vietnameseFirstNames.length)];
      return `${adjective} ${firstName}`;
    }
  ];

  const randomFormat = formats[Math.floor(Math.random() * formats.length)];
  return randomFormat();
}

/**
 * Generate a secure random password that meets all requirements
 */
export function generateRandomPassword(length: number = 12): string {
  if (length < 8) length = 8;
  if (length > 265) length = 265;

  let password = '';
  let attempts = 0;
  const maxAttempts = 100;

  while (attempts < maxAttempts) {
    password = '';
    
    // Ensure at least one character from each required category
    password += getRandomChar(lowercase); // At least 1 lowercase
    password += getRandomChar(uppercase); // At least 1 uppercase  
    password += getRandomChar(numbers);   // At least 1 number
    password += getRandomChar(symbols);   // At least 1 symbol

    // Fill the rest with random characters from all sets
    const allChars = lowercase + uppercase + numbers + symbols;
    for (let i = 4; i < length; i++) {
      password += getRandomChar(allChars);
    }

    // Shuffle the password to avoid predictable patterns
    password = shuffleString(password);

    // Check if password meets all requirements
    if (isPasswordValid(password)) {
      break;
    }

    attempts++;
  }

  return password;
}

/**
 * Get a random character from a string
 */
function getRandomChar(str: string): string {
  return str.charAt(Math.floor(Math.random() * str.length));
}

/**
 * Shuffle a string randomly
 */
function shuffleString(str: string): string {
  const arr = str.split('');
  for (let i = arr.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [arr[i], arr[j]] = [arr[j], arr[i]];
  }
  return arr.join('');
}

/**
 * Check if password meets all requirements
 */
function isPasswordValid(password: string): boolean {
  // Length check
  if (password.length < 8 || password.length > 265) return false;

  // Must contain at least one lowercase letter
  if (!/[a-z]/.test(password)) return false;

  // Must contain at least one uppercase letter  
  if (!/[A-Z]/.test(password)) return false;

  // Must contain at least one number
  if (!/[0-9]/.test(password)) return false;

  // Must contain at least one symbol
  if (!/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password)) return false;

  // Check against weak passwords
  const lowerPassword = password.toLowerCase();
  if (weakPasswords.some(weak => lowerPassword.includes(weak))) return false;

  // Check for sequential characters (abc, 123, etc.)
  if (hasSequentialChars(password)) return false;

  // Check for repeated characters (aaa, 111, etc.)
  if (hasRepeatedChars(password)) return false;

  return true;
}

/**
 * Check for sequential characters
 */
function hasSequentialChars(password: string): boolean {
  const sequences = ['abc', '123', 'qwe', 'asd', 'zxc'];
  const lowerPassword = password.toLowerCase();
  
  for (const seq of sequences) {
    if (lowerPassword.includes(seq)) return true;
    // Check reverse sequences
    if (lowerPassword.includes(seq.split('').reverse().join(''))) return true;
  }
  
  return false;
}

/**
 * Check for repeated characters (3 or more in a row)
 */
function hasRepeatedChars(password: string): boolean {
  for (let i = 0; i < password.length - 2; i++) {
    if (password[i] === password[i + 1] && password[i] === password[i + 2]) {
      return true;
    }
  }
  return false;
}

/**
 * Generate multiple password options
 */
export function generatePasswordOptions(count: number = 3): string[] {
  const passwords: string[] = [];
  const lengths = [12, 14, 16]; // Different lengths for variety
  
  for (let i = 0; i < count; i++) {
    const length = lengths[i % lengths.length];
    passwords.push(generateRandomPassword(length));
  }
  
  return passwords;
}
