import { databases } from '$lib/appwrite';
import { ID } from 'appwrite';

const databaseId = '686a3883000ac5847c3d';
const usersCollectionId = 'users';

export async function ensureUserProfile(user: any) {
  try {
    // Try to get existing profile
    const existingProfile = await databases.getDocument(databaseId, usersCollectionId, user.id);
    return existingProfile;
  } catch (err: any) {
    if (err.code === 404) {
      // Profile doesn't exist, create it
      try {
        const newProfile = await databases.createDocument(
          databaseId,
          usersCollectionId,
          user.id,
          {
            email: user.email.toLowerCase(),
            displayName: user.displayName || user.email.split('@')[0],
            avatarUrl: user.avatarUrl || '',
            createdAt: new Date().toISOString()
          }
        );
        console.log('Created user profile:', newProfile);
        return newProfile;
      } catch (createErr) {
        console.error('Failed to create user profile:', createErr);
        throw createErr;
      }
    } else {
      console.error('Error checking user profile:', err);
      throw err;
    }
  }
}

export async function updateUserProfile(userId: string, updates: any) {
  try {
    const updatedProfile = await databases.updateDocument(
      databaseId,
      usersCollectionId,
      userId,
      updates
    );
    return updatedProfile;
  } catch (err) {
    console.error('Error updating user profile:', err);
    throw err;
  }
}

export async function searchUsersByEmail(email: string) {
  try {
    const results = await databases.listDocuments(databaseId, usersCollectionId, [
      // Note: Query.equal might need to be imported from appwrite
      // Query.equal('email', email.toLowerCase())
    ]);
    return results.documents;
  } catch (err) {
    console.error('Error searching users by email:', err);
    throw err;
  }
}

export async function searchUsersByDisplayName(displayName: string) {
  try {
    const results = await databases.listDocuments(databaseId, usersCollectionId, [
      // Note: Query.search might need to be imported from appwrite
      // Query.search('displayName', displayName)
    ]);
    return results.documents;
  } catch (err) {
    console.error('Error searching users by display name:', err);
    throw err;
  }
}
