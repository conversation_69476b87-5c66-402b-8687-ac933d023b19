import { databases } from '$lib/appwrite';
import { Query } from 'appwrite';
import { DATABASE_CONFIG } from '$lib/constants/database';

export interface UserProfile {
  $id: string;
  email: string;
  displayName: string;
  name: string;
  avatarUrl?: string;
  userId: string;
  emailVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export async function ensureUserProfile(user: any): Promise<UserProfile | null> {
  try {
    console.log('ensureUserProfile called for user:', user.$id);

    console.log('Found existing profile, updating...');

    // Update profile with latest auth data (only if needed)
    const updates = {
      email: user.email.toLowerCase(),
      displayName: user.displayName || user.name || '',
      name: user.name || user.displayName || '',
      emailVerified: user.emailVerification || false,
      updatedAt: new Date().toISOString()
    };

    const updatedProfile = await databases.updateDocument(
      DATABASE_CONFIG.DATABASE_ID,
      DATABASE_CONFIG.COLLECTIONS.USERS,
      user.$id,
      updates
    );

    console.log('Profile updated successfully');
    return updatedProfile as unknown as UserProfile;
  } catch (err: any) {
    if (err.code === 404) {
      // Profile doesn't exist, create it
      try {
        console.log('Profile not found, creating new profile...');

        const newProfile = await databases.createDocument(
          DATABASE_CONFIG.DATABASE_ID,
          DATABASE_CONFIG.COLLECTIONS.USERS,
          user.$id,
          {
            email: user.email.toLowerCase(),
            displayName: user.displayName || user.name || user.email.split('@')[0],
            name: user.name || user.displayName || user.email.split('@')[0],
            avatarUrl: user.prefs?.avatarUrl || '',
            userId: user.$id,
            emailVerified: user.emailVerification || false,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        );
        console.log('Created user profile successfully:', newProfile.$id);
        return newProfile as unknown as UserProfile;
      } catch (createErr: any) {
        console.error('Failed to create user profile:', createErr);
        // Don't throw error, just return null to prevent loop
        return null;
      }
    } else {
      console.error('Error checking user profile:', err);
      // Don't throw error, just return null to prevent loop
      return null;
    }
  }
}

export async function updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<UserProfile> {
  try {
    const updatedProfile = await databases.updateDocument(
      DATABASE_CONFIG.DATABASE_ID,
      DATABASE_CONFIG.COLLECTIONS.USERS,
      userId,
      {
        ...updates,
        updatedAt: new Date().toISOString()
      }
    );
    return updatedProfile as unknown as UserProfile;
  } catch (err) {
    console.error('Error updating user profile:', err);
    throw err;
  }
}

export async function getUserProfile(userId: string): Promise<UserProfile> {
  try {
    const profile = await databases.getDocument(
      DATABASE_CONFIG.DATABASE_ID,
      DATABASE_CONFIG.COLLECTIONS.USERS,
      userId
    );
    return profile as unknown as UserProfile;
  } catch (err) {
    console.error('Failed to get user profile:', err);
    throw err;
  }
}

export async function searchUsers(query: string): Promise<UserProfile[]> {
  try {
    console.log('Searching users with query:', query);

    // Get all users and filter client-side for better compatibility
    const allUsers = await databases.listDocuments(
      DATABASE_CONFIG.DATABASE_ID,
      DATABASE_CONFIG.COLLECTIONS.USERS
    );

    console.log('Total users in database:', allUsers.documents.length);

    const searchQuery = query.toLowerCase();
    const matches = allUsers.documents.filter((doc: any) => {
      const email = doc.email?.toLowerCase() || '';
      const displayName = doc.displayName?.toLowerCase() || '';
      const name = doc.name?.toLowerCase() || '';

      return email.includes(searchQuery) ||
             displayName.includes(searchQuery) ||
             name.includes(searchQuery);
    });

    console.log('Search matches found:', matches.length);
    return matches as unknown as UserProfile[];
  } catch (err) {
    console.error('Failed to search users:', err);
    throw err;
  }
}


