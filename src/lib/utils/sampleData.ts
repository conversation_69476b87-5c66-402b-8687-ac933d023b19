import { databases } from '$lib/appwrite';
import { ID, Permission, Role } from 'appwrite';

const databaseId = '686a3883000ac5847c3d';
const roomsCollectionId = '686a38b00033c187fde6';
const messagesCollectionId = 'messages';

export async function createSampleRooms(userId: string) {
  try {
    // Create sample rooms
    const sampleRooms = [
      {
        name: '<PERSON>òng chung',
        isGroup: true,
        passwordProtected: true,
        password: 'demo123'
      },
      {
        name: '<PERSON>t riêng với Admin',
        isGroup: false,
        passwordProtected: true,
        password: 'private123'
      },
      {
        name: '<PERSON><PERSON><PERSON><PERSON> học tập',
        isGroup: true,
        passwordProtected: true,
        password: 'study123'
      }
    ];

    const createdRooms = [];
    
    for (const room of sampleRooms) {
      try {
        const createdRoom = await databases.createDocument(
          databaseId,
          roomsCollectionId,
          ID.unique(),
          {
            name: room.name,
            isGroup: room.isGroup,
            passwordProtected: room.passwordProtected,
            password: room.password,
            createdBy: userId,
            members: [userId]
          },
          [
            Permission.read(Role.user(userId)),
            Permission.update(Role.user(userId)),
            Permission.delete(Role.user(userId))
          ]
        );
        createdRooms.push(createdRoom);
        console.log(`Created room: ${room.name}`);
      } catch (err) {
        console.error(`Error creating room ${room.name}:`, err);
      }
    }

    return createdRooms;
  } catch (error) {
    console.error('Error creating sample rooms:', error);
    throw error;
  }
}

export async function createSampleMessages(roomId: string, userId: string, senderName: string) {
  try {
    const sampleMessages = [
      'Chào mọi người! 👋',
      'Hôm nay thế nào?',
      'Có ai online không?',
      'Mình vừa test tính năng chat realtime',
      'Rất tuyệt! 🎉'
    ];

    const createdMessages = [];

    for (let i = 0; i < sampleMessages.length; i++) {
      try {
        const message = await databases.createDocument(
          databaseId,
          messagesCollectionId,
          ID.unique(),
          {
            roomId,
            senderName,
            text: sampleMessages[i],
            senderId: userId
          },
          [
            Permission.read(Role.user(userId)),
            Permission.update(Role.user(userId)),
            Permission.delete(Role.user(userId))
          ]
        );
        createdMessages.push(message);
        
        // Add delay between messages to simulate real conversation
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (err) {
        console.error(`Error creating message: ${sampleMessages[i]}`, err);
      }
    }

    return createdMessages;
  } catch (error) {
    console.error('Error creating sample messages:', error);
    throw error;
  }
}
