import { writable, type Writable } from "svelte/store";

export interface UserProfile {
  id: string;
  email: string;
  displayName: string;
  avatarUrl?: string;
  emailVerified: boolean;
}

export const user: Writable<UserProfile | null> = writable(null);
export const isAuthenticated = writable(false);
export const authLoading = writable(false);
export const authError = writable<string | null>(null);

// Chat messages for current chat room
export interface ChatMessage {
  id: string;
  roomId: string;
  senderId: string;
  text: string;
  createdAt: string;
}
export const chatMessages: Writable<ChatMessage[]> = writable([]);
