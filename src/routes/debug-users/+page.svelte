<script lang="ts">
  import { onMount } from 'svelte';
  import { databases } from '$lib/appwrite';
  import { Query } from 'appwrite';

  const databaseId = '686a3883000ac5847c3d';
  const usersCollectionId = '686a3fc900213085a7b3';

  let users: any[] = [];
  let loading = true;
  let error = '';
  let searchQuery = '';
  let searchResults: any[] = [];
  let searchError = '';

  onMount(async () => {
    await loadUsers();
  });

  async function loadUsers() {
    try {
      const result = await databases.listDocuments(databaseId, usersCollectionId);
      users = result.documents;
      console.log('Users loaded:', users);
    } catch (err: any) {
      console.error('Failed to load users:', err);
      error = err.message;
    } finally {
      loading = false;
    }
  }

  async function testSearch() {
    if (!searchQuery.trim()) return;
    
    searchError = '';
    searchResults = [];
    
    console.log('Testing search for:', searchQuery);
    
    // Test different search methods
    const tests = [
      {
        name: 'Search by displayName',
        query: () => databases.listDocuments(databaseId, usersCollectionId, [
          Query.search('displayName', searchQuery)
        ])
      },
      {
        name: 'Search by name',
        query: () => databases.listDocuments(databaseId, usersCollectionId, [
          Query.search('name', searchQuery)
        ])
      },
      {
        name: 'Equal email',
        query: () => databases.listDocuments(databaseId, usersCollectionId, [
          Query.equal('email', searchQuery)
        ])
      },
      {
        name: 'Equal userId',
        query: () => databases.listDocuments(databaseId, usersCollectionId, [
          Query.equal('userId', searchQuery)
        ])
      },
      {
        name: 'Contains displayName',
        query: () => databases.listDocuments(databaseId, usersCollectionId, [
          Query.contains('displayName', searchQuery)
        ])
      }
    ];

    for (const test of tests) {
      try {
        console.log(`Testing: ${test.name}`);
        const result = await test.query();
        console.log(`${test.name} success:`, result.documents);
        if (result.documents.length > 0) {
          searchResults = [...searchResults, ...result.documents];
        }
      } catch (err: any) {
        console.log(`${test.name} failed:`, err.message);
      }
    }

    // Remove duplicates
    searchResults = searchResults.filter((user, index, self) => 
      index === self.findIndex(u => u.$id === user.$id)
    );

    if (searchResults.length === 0) {
      searchError = 'No results found with any search method';
    }
  }
</script>

<main class="p-8 max-w-6xl mx-auto min-h-screen bg-gradient-to-br from-white to-gray-100">
  <h1 class="text-3xl font-bold mb-6">Users Collection Debug</h1>
  
  <!-- Users List -->
  <div class="bg-white p-6 rounded-lg shadow border mb-6">
    <h2 class="text-xl font-semibold mb-4">All Users in Collection</h2>
    
    {#if loading}
      <div class="text-center py-4">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-black mx-auto mb-2"></div>
        <p>Loading users...</p>
      </div>
    {:else if error}
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <strong>Error:</strong> {error}
      </div>
    {:else if users.length === 0}
      <p class="text-gray-500">No users found in collection.</p>
    {:else}
      <div class="space-y-4">
        <p class="text-sm text-gray-600">Found {users.length} users:</p>
        {#each users as user}
          <div class="border border-gray-200 rounded p-4 bg-gray-50">
            <h3 class="font-semibold">User ID: {user.$id}</h3>
            <div class="mt-2 text-sm space-y-1">
              <p><strong>Email:</strong> {user.email || 'N/A'}</p>
              <p><strong>Display Name:</strong> {user.displayName || 'N/A'}</p>
              <p><strong>Name:</strong> {user.name || 'N/A'}</p>
              <p><strong>User ID:</strong> {user.userId || 'N/A'}</p>
              <p><strong>Avatar URL:</strong> {user.avatarUrl || 'N/A'}</p>
            </div>
            <details class="mt-2">
              <summary class="cursor-pointer text-blue-600 text-sm">Show all attributes</summary>
              <pre class="text-xs bg-gray-100 p-2 rounded mt-2 overflow-auto">{JSON.stringify(user, null, 2)}</pre>
            </details>
          </div>
        {/each}
      </div>
    {/if}
  </div>

  <!-- Search Test -->
  <div class="bg-white p-6 rounded-lg shadow border">
    <h2 class="text-xl font-semibold mb-4">Search Test</h2>
    
    <div class="flex gap-4 mb-4">
      <input
        type="text"
        bind:value={searchQuery}
        placeholder="Enter search query..."
        class="flex-1 border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
      <button
        on:click={testSearch}
        class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
      >
        Test Search
      </button>
    </div>

    {#if searchError}
      <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
        {searchError}
      </div>
    {/if}

    {#if searchResults.length > 0}
      <div class="space-y-2">
        <h3 class="font-semibold">Search Results ({searchResults.length}):</h3>
        {#each searchResults as result}
          <div class="border border-green-200 rounded p-3 bg-green-50">
            <p><strong>ID:</strong> {result.$id}</p>
            <p><strong>Display Name:</strong> {result.displayName || 'N/A'}</p>
            <p><strong>Email:</strong> {result.email || 'N/A'}</p>
          </div>
        {/each}
      </div>
    {/if}
  </div>

  <!-- Navigation -->
  <div class="mt-6 flex gap-4">
    <a href="/room" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition">
      Back to Room
    </a>
    <a href="/friends" class="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition">
      Friends Page
    </a>
  </div>
</main>
