<script lang="ts">
  import { writable } from "svelte/store";
  import { Account } from 'appwrite';
  import { client, databases } from "$lib/appwrite";
  import { user, isAuthenticated, authLoading, authError } from '$lib/stores/auth';
  import { goto } from '$app/navigation';
  import { z } from 'zod';
  import { onMount } from 'svelte';

  let authMode: 'login' | 'register' | 'forgot' | 'reset' = 'login';
  let email: string = '';
  let password: string = '';
  let displayName: string = '';
  let verificationSent: boolean = false;
  let emailVerified: boolean = false;
  let forgotSent: boolean = false;
  let resetCode: string = '';
  let newPassword: string = '';
  let resetSuccess: boolean = false;
  let validationError: string = '';
  let resetUserId: string = '';
  let resetSecret: string = '';
  let checkingSession = true;

  const account = new Account(client);

  // Zod schemas cho từng form
  const loginSchema = z.object({
    email: z.string().email({ message: '<PERSON><PERSON> không hợp lệ.' }),
    password: z.string().min(6, { message: '<PERSON><PERSON><PERSON> khẩu phải từ 6 ký tự trở lên.' })
  });
  const registerSchema = loginSchema.extend({
    displayName: z.string().min(1, { message: 'Tên hiển thị không được để trống.' })
  });
  const forgotSchema = z.object({
    email: z.string().email({ message: 'Email không hợp lệ.' })
  });
  const resetSchema = z.object({
    newPassword: z.string().min(6, { message: 'Mật khẩu mới phải từ 6 ký tự trở lên.' })
  });

  async function handleRegister(): Promise<void> {
    validationError = '';
    const result = registerSchema.safeParse({ email, password, displayName });
    if (!result.success) {
      validationError = result.error.errors[0].message;
      return;
    }
    authLoading.set(true);
    authError.set(null);
    try {
      const newUser = await account.create('unique()', email, password, displayName);
      await handleLogin();

      // Create user profile in database
      try {
        await databases.createDocument('686a3883000ac5847c3d', 'users', newUser.$id, {
          email: email.toLowerCase(),
          displayName: displayName,
          avatarUrl: '',
          createdAt: new Date().toISOString()
        });
      } catch (profileErr) {
        console.log('Failed to create user profile:', profileErr);
        // Continue anyway, profile can be created later
      }

      await account.createVerification(window.location.origin + '/auth');
      verificationSent = true;
    } catch (err: any) {
      authError.set(err.message || 'Registration failed');
    } finally {
      authLoading.set(false);
    }
  }

  async function handleLogin(): Promise<void> {
    validationError = '';
    const result = loginSchema.safeParse({ email, password });
    if (!result.success) {
      validationError = result.error.errors[0].message;
      return;
    }
    authLoading.set(true);
    authError.set(null);
    try {
      await account.createEmailPasswordSession(email, password);
      const acc = await account.get();
      user.set({
        id: acc.$id,
        email: acc.email,
        displayName: acc.name,
        avatarUrl: acc.prefs?.avatarUrl || '',
        emailVerified: acc.emailVerification
      });
      isAuthenticated.set(true);
      emailVerified = acc.emailVerification;
      if (acc.emailVerification) {
        goto('/room');
      }
    } catch (err: any) {
      authError.set(err.message || 'Login failed');
    } finally {
      authLoading.set(false);
    }
  }

  async function resendVerification(): Promise<void> {
    try {
      await account.createVerification(window.location.origin + '/auth');
      verificationSent = true;
    } catch {}
  }

  async function refreshEmailVerification(): Promise<void> {
    try {
      const acc = await account.get();
      emailVerified = acc.emailVerification;
      if (acc.emailVerification) {
        user.set({
          id: acc.$id,
          email: acc.email,
          displayName: acc.name,
          avatarUrl: acc.prefs?.avatarUrl || '',
          emailVerified: acc.emailVerification
        });
        goto('/room');
      }
    } catch {}
  }

  async function handleForgot(): Promise<void> {
    validationError = '';
    const result = forgotSchema.safeParse({ email });
    if (!result.success) {
      validationError = result.error.errors[0].message;
      return;
    }
    authLoading.set(true);
    authError.set(null);
    forgotSent = false;
    try {
      await account.createRecovery(email, window.location.origin + '/auth?mode=reset');
      forgotSent = true;
    } catch (err: any) {
      authError.set(err.message || 'Gửi email thất bại');
    } finally {
      authLoading.set(false);
    }
  }

  async function handleReset(): Promise<void> {
    validationError = '';
    const result = resetSchema.safeParse({ newPassword });
    if (!result.success) {
      validationError = result.error.errors[0].message;
      return;
    }
    authLoading.set(true);
    authError.set(null);
    resetSuccess = false;
    try {
      await account.updateRecovery(resetUserId, resetSecret, newPassword);
      resetSuccess = true;
      setTimeout(() => {
        authMode = 'login';
      }, 1500);
    } catch (err: any) {
      authError.set(err.message || 'Đặt lại mật khẩu thất bại');
    } finally {
      authLoading.set(false);
    }
  }

  // Detect reset code from URL
  if (typeof window !== 'undefined') {
    const params = new URLSearchParams(window.location.search);
    if (params.get('mode') === 'reset' && params.get('secret') && params.get('userId')) {
      authMode = 'reset';
      resetUserId = params.get('userId') ?? '';
      resetSecret = params.get('secret') ?? '';
    }
  }

  onMount(async () => {
    try {
      const acc = await account.get();
      user.set({
        id: acc.$id,
        email: acc.email,
        displayName: acc.name,
        avatarUrl: acc.prefs?.avatarUrl || '',
        emailVerified: acc.emailVerification
      });
      isAuthenticated.set(true);
      goto('/room');
    } catch {
      // Không có session, cho phép ở lại trang auth
      user.set(null);
      isAuthenticated.set(false);
    } finally {
      checkingSession = false;
    }
  });
</script>

<main class="flex flex-col items-center p-5 min-h-screen bg-gradient-to-br from-white to-gray-100">
  {#if !checkingSession}
    <section class="w-full max-w-md mx-auto mt-16 mb-8 p-8 bg-white rounded-2xl shadow-xl border border-gray-200 animate-fade-in">
      {#if authMode === 'login'}
        <h2 class="text-2xl font-bold mb-6 text-center text-black tracking-tight">Đăng nhập</h2>
        {#if validationError}
          <div class="mb-4 px-4 py-2 bg-gray-100 text-gray-700 rounded text-center border border-gray-300">{validationError}</div>
        {/if}
        {#if $authError}
          <div class="mb-4 px-4 py-2 bg-gray-100 text-gray-700 rounded text-center border border-gray-300">{$authError}</div>
        {/if}
        <form on:submit|preventDefault={handleLogin} class="flex flex-col gap-4">
          <input class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black transition bg-white text-black" type="email" placeholder="Email" bind:value={email} required />
          <input class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black transition bg-white text-black" type="password" placeholder="Mật khẩu" bind:value={password} required />
          <button type="submit" class="w-full py-2 rounded-lg bg-black hover:bg-gray-900 text-white font-semibold transition disabled:opacity-60" disabled={$authLoading}>
            Đăng nhập
          </button>
        </form>
        <div class="mt-4 text-sm text-center">
          <button class="text-black underline hover:text-gray-700 font-medium" on:click={() => {authMode = 'register'; validationError = ''}}>Đăng ký tài khoản</button>
          <span class="mx-2">|</span>
          <button class="text-black underline hover:text-gray-700 font-medium" on:click={() => {authMode = 'forgot'; validationError = ''}}>Quên mật khẩu?</button>
        </div>
      {/if}
      {#if authMode === 'register'}
        <h2 class="text-2xl font-bold mb-6 text-center text-black tracking-tight">Đăng ký tài khoản</h2>
        {#if validationError}
          <div class="mb-4 px-4 py-2 bg-gray-100 text-gray-700 rounded text-center border border-gray-300">{validationError}</div>
        {/if}
        {#if $authError}
          <div class="mb-4 px-4 py-2 bg-gray-100 text-gray-700 rounded text-center border border-gray-300">{$authError}</div>
        {/if}
        <form on:submit|preventDefault={handleRegister} class="flex flex-col gap-4">
          <input class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black transition bg-white text-black" type="email" placeholder="Email" bind:value={email} required />
          <input class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black transition bg-white text-black" type="password" placeholder="Mật khẩu" bind:value={password} required />
          <input class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black transition bg-white text-black" type="text" placeholder="Tên hiển thị" bind:value={displayName} required />
          <button type="submit" class="w-full py-2 rounded-lg bg-black hover:bg-gray-900 text-white font-semibold transition disabled:opacity-60" disabled={$authLoading}>
            Đăng ký
          </button>
        </form>
        <div class="mt-4 text-sm text-center">
          <button class="text-black underline hover:text-gray-700 font-medium" on:click={() => {authMode = 'login'; validationError = ''}}>Đã có tài khoản? Đăng nhập</button>
        </div>
      {/if}
      {#if authMode === 'forgot'}
        <h2 class="text-2xl font-bold mb-6 text-center text-black tracking-tight">Quên mật khẩu</h2>
        {#if validationError}
          <div class="mb-4 px-4 py-2 bg-gray-100 text-gray-700 rounded text-center border border-gray-300">{validationError}</div>
        {/if}
        {#if $authError}
          <div class="mb-4 px-4 py-2 bg-gray-100 text-gray-700 rounded text-center border border-gray-300">{$authError}</div>
        {/if}
        {#if forgotSent}
          <div class="mb-4 px-4 py-2 bg-gray-100 text-gray-700 rounded text-center border border-gray-300">Đã gửi email đặt lại mật khẩu. Hãy kiểm tra hộp thư!</div>
        {/if}
        <form on:submit|preventDefault={handleForgot} class="flex flex-col gap-4">
          <input class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black transition bg-white text-black" type="email" placeholder="Email" bind:value={email} required />
          <button type="submit" class="w-full py-2 rounded-lg bg-black hover:bg-gray-900 text-white font-semibold transition disabled:opacity-60" disabled={$authLoading}>
            Gửi email đặt lại mật khẩu
          </button>
        </form>
        <div class="mt-4 text-sm text-center">
          <button class="text-black underline hover:text-gray-700 font-medium" on:click={() => {authMode = 'login'; validationError = ''}}>Quay lại đăng nhập</button>
        </div>
      {/if}
      {#if authMode === 'reset'}
        <h2 class="text-2xl font-bold mb-6 text-center text-black tracking-tight">Đặt lại mật khẩu</h2>
        {#if validationError}
          <div class="mb-4 px-4 py-2 bg-gray-100 text-gray-700 rounded text-center border border-gray-300">{validationError}</div>
        {/if}
        {#if $authError}
          <div class="mb-4 px-4 py-2 bg-gray-100 text-gray-700 rounded text-center border border-gray-300">{$authError}</div>
        {/if}
        {#if resetSuccess}
          <div class="mb-4 px-4 py-2 bg-gray-100 text-gray-700 rounded text-center border border-gray-300">Đặt lại mật khẩu thành công! Đang chuyển về đăng nhập...</div>
        {/if}
        <form on:submit|preventDefault={handleReset} class="flex flex-col gap-4">
          <input class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black transition bg-white text-black" type="password" placeholder="Mật khẩu mới" bind:value={newPassword} required />
          <button type="submit" class="w-full py-2 rounded-lg bg-black hover:bg-gray-900 text-white font-semibold transition disabled:opacity-60" disabled={$authLoading}>
            Đặt lại mật khẩu
          </button>
        </form>
      {/if}
      {#if $isAuthenticated && !emailVerified}
        <div class="mt-8">
          <h3 class="text-lg font-semibold mb-2 text-black">Xác thực email</h3>
          <div class="mb-4 text-gray-700">Vui lòng kiểm tra hộp thư và xác thực email để tiếp tục.</div>
          {#if verificationSent}
            <div class="mb-4 px-4 py-2 bg-gray-100 text-gray-700 rounded text-center border border-gray-300">Đã gửi email xác thực. Hãy kiểm tra hộp thư!</div>
          {/if}
          <button class="w-full py-2 rounded-lg bg-black hover:bg-gray-900 text-white font-semibold mb-2 transition" on:click={resendVerification}>Gửi lại email xác thực</button>
          <button class="text-black underline text-sm block mx-auto" on:click={refreshEmailVerification}>Tôi đã xác thực, làm mới</button>
        </div>
      {/if}
    </section>
  {/if}
</main>
