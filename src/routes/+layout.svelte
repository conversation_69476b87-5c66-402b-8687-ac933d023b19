<script lang="ts">
  import '../app.css';
  import { onMount } from 'svelte';
  import { user, isAuthenticated, authLoading } from '$lib/stores/auth';
  import { account } from '$lib/appwrite';

  // Check authentication on app load
  onMount(async () => {
    authLoading.set(true);
    try {
      const acc = await account.get();
      user.set({
        id: acc.$id,
        email: acc.email,
        displayName: acc.name,
        avatarUrl: acc.prefs?.avatarUrl || '',
        emailVerified: acc.emailVerification
      });
      isAuthenticated.set(true);
    } catch {
      user.set(null);
      isAuthenticated.set(false);
    } finally {
      authLoading.set(false);
    }
  });
</script>

<main class="layout">
  {#if $authLoading}
    <div class="flex items-center justify-center min-h-screen bg-gradient-to-br from-white to-gray-100">
      <div class="w-full max-w-md mx-auto p-8 bg-white rounded-2xl shadow-xl border border-gray-200 animate-fade-in text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto mb-4"></div>
        <h2 class="text-xl font-semibold text-black mb-2">Đang kiểm tra phiên đăng nhập...</h2>
        <p class="text-gray-600">Vui lòng chờ trong giây lát</p>
      </div>
    </div>
  {:else}
    <slot />
  {/if}
</main>

<style>
.layout {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  font-family: 'Inter', sans-serif;
  color: #222;
}
</style>
