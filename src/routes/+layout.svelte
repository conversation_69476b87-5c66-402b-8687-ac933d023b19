<script lang="ts">
  import '../app.css';
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { user, isAuthenticated, authLoading } from '$lib/stores/auth';
  import { account } from '$lib/appwrite';

  // Check authentication on app load
  onMount(async () => {
    authLoading.set(true);
    try {
      const acc = await account.get();
      user.set({
        id: acc.$id,
        email: acc.email,
        displayName: acc.name,
        avatarUrl: acc.prefs?.avatarUrl || '',
        emailVerified: acc.emailVerification
      });
      isAuthenticated.set(true);
    } catch {
      user.set(null);
      isAuthenticated.set(false);
    } finally {
      authLoading.set(false);
    }
  });
</script>

<main class="layout">
  {#if $authLoading}
    <div class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto mb-4"></div>
        <p class="text-gray-600"><PERSON><PERSON> kiểm tra phiên đăng nhập...</p>
      </div>
    </div>
  {:else}
    <slot />
  {/if}
</main>

<style>
.layout {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  font-family: 'Inter', sans-serif;
  color: #222;
}
</style>
