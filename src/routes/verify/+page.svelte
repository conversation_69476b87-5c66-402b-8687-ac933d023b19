<script lang="ts">
  import { onMount } from 'svelte';

  import { goto } from '$app/navigation';
  import { account } from '$lib/appwrite';
  import { user, isAuthenticated } from '$lib/stores/auth';
  import { ensureUserProfile } from '$lib/utils/userProfile';

  let verificationStatus: 'loading' | 'success' | 'error' | 'expired' = 'loading';
  let errorMessage = '';

  onMount(async () => {
    // Get URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const userId = urlParams.get('userId');
    const secret = urlParams.get('secret');

    console.log('Verification URL params:', { userId, secret });

    if (!userId || !secret) {
      verificationStatus = 'error';
      errorMessage = 'URL verification không hợp lệ. Thiếu userId hoặc secret.';
      console.error('Missing verification parameters');
      return;
    }

    try {
      // First check if user is already verified
      try {
        const currentUser = await account.get();
        console.log('Current user status:', currentUser);

        if (currentUser.emailVerification) {
          console.log('User already verified, ensuring profile exists and redirecting');

          // Ensure user profile exists for already verified users
          try {
            await ensureUserProfile(currentUser);
            console.log('User profile ensured for verified user');
          } catch (profileErr) {
            console.error('Failed to ensure user profile:', profileErr);
          }

          user.set({
            id: currentUser.$id,
            email: currentUser.email,
            displayName: currentUser.name,
            avatarUrl: currentUser.prefs?.avatarUrl || '',
            emailVerified: currentUser.emailVerification
          });
          isAuthenticated.set(true);
          verificationStatus = 'success';

          setTimeout(() => {
            goto('/room');
          }, 2000);
          return;
        }
      } catch (userErr) {
        console.log('Could not get current user, proceeding with verification');
      }

      console.log('Attempting to verify email with userId:', userId, 'secret:', secret);

      // Verify email using Appwrite
      const verificationResult = await account.updateVerification(userId, secret);
      console.log('Verification result:', verificationResult);

      // Get updated user info after verification
      const updatedUser = await account.get();
      console.log('Updated user after verification:', updatedUser);

      // Now create user profile in database after successful verification
      console.log('Creating user profile after email verification...');
      try {
        await ensureUserProfile(updatedUser);
        console.log('User profile created successfully');
      } catch (profileErr) {
        console.error('Failed to create user profile:', profileErr);
        // Continue anyway - profile creation failure shouldn't block verification
      }

      user.set({
        id: updatedUser.$id,
        email: updatedUser.email,
        displayName: updatedUser.name,
        avatarUrl: updatedUser.prefs?.avatarUrl || '',
        emailVerified: updatedUser.emailVerification
      });
      isAuthenticated.set(true);

      verificationStatus = 'success';
      console.log('Email verification successful');

      // Redirect to room page after 3 seconds
      setTimeout(() => {
        goto('/room');
      }, 3000);

    } catch (err: any) {
      console.error('Email verification failed:', err);

      // Check specific error cases
      if (err.code === 401 && err.message?.includes('already verified')) {
        // User is already verified but we couldn't detect it earlier
        console.log('User already verified (detected in error), redirecting');
        verificationStatus = 'success';
        setTimeout(() => {
          goto('/room');
        }, 2000);
        return;
      }

      verificationStatus = 'error';

      if (err.code === 401) {
        errorMessage = 'Link verification đã hết hạn hoặc không hợp lệ.';
      } else if (err.code === 404) {
        errorMessage = 'Không tìm thấy user hoặc verification request.';
      } else if (err.message?.includes('already verified')) {
        errorMessage = 'Tài khoản đã được xác thực trước đó.';
        verificationStatus = 'success';
        setTimeout(() => {
          goto('/room');
        }, 2000);
        return;
      } else {
        errorMessage = err.message || 'Có lỗi xảy ra khi verify email.';
      }
    }
  });

  async function resendVerification() {
    try {
      // First check if user is already verified
      const currentUser = await account.get();
      if (currentUser.emailVerification) {
        alert('Tài khoản đã được xác thực rồi! Đang chuyển hướng...');
        user.set({
          id: currentUser.$id,
          email: currentUser.email,
          displayName: currentUser.name,
          avatarUrl: currentUser.prefs?.avatarUrl || '',
          emailVerified: currentUser.emailVerification
        });
        isAuthenticated.set(true);
        verificationStatus = 'success';
        setTimeout(() => {
          goto('/room');
        }, 1000);
        return;
      }

      // If not verified, send new verification email
      await account.createVerification(window.location.origin + '/verify');
      alert('Đã gửi lại email verification! Vui lòng kiểm tra hộp thư.');
    } catch (err: any) {
      console.error('Resend verification error:', err);
      if (err.message?.includes('already verified')) {
        alert('Tài khoản đã được xác thực rồi! Đang chuyển hướng...');
        setTimeout(() => {
          goto('/room');
        }, 1000);
      } else {
        alert('Không thể gửi lại email: ' + err.message);
      }
    }
  }
</script>

<svelte:head>
  <title>Email Verification - Chat App</title>
</svelte:head>

<main class="min-h-screen bg-gradient-to-br from-white to-gray-100 flex items-center justify-center p-4">
  <div class="w-full max-w-md mx-auto">
    <div class="bg-white rounded-2xl shadow-xl border border-gray-200 p-8 text-center animate-fade-in">
      {#if verificationStatus === 'loading'}
        <div class="mb-6">
          <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
          <h2 class="text-xl font-semibold text-gray-900 mb-2">Đang xác thực email...</h2>
          <p class="text-gray-600 text-sm">Vui lòng chờ trong giây lát</p>
        </div>
      {:else if verificationStatus === 'success'}
        <div class="mb-6">
          <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <h2 class="text-xl font-semibold text-green-900 mb-2">Email đã được xác thực!</h2>
          <p class="text-green-700 text-sm mb-4">
            Tài khoản của bạn đã được kích hoạt thành công. Bạn có thể bắt đầu sử dụng ứng dụng.
          </p>
          <p class="text-gray-600 text-sm">
            Đang chuyển hướng đến trang chính...
          </p>
        </div>
        
        <div class="flex justify-center">
          <a 
            href="/room" 
            class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition font-medium"
          >
            Tiếp tục
          </a>
        </div>
      {:else if verificationStatus === 'error'}
        <div class="mb-6">
          <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </div>
          <h2 class="text-xl font-semibold text-red-900 mb-2">Xác thực thất bại</h2>
          <p class="text-red-700 text-sm mb-4">{errorMessage}</p>
        </div>
        
        <div class="space-y-3">
          <button 
            class="w-full px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition font-medium"
            on:click={resendVerification}
          >
            Gửi lại email xác thực
          </button>
          <a 
            href="/auth" 
            class="block w-full px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition font-medium"
          >
            Quay lại đăng nhập
          </a>
        </div>
      {/if}
    </div>
    
    <!-- Help text -->
    <div class="mt-6 text-center">
      <p class="text-gray-500 text-sm">
        Gặp vấn đề? 
        <a href="/auth" class="text-blue-600 hover:underline">Quay lại trang đăng nhập</a>
      </p>
    </div>
  </div>
</main>
