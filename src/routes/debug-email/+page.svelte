<script lang="ts">
  import { onMount } from 'svelte';
  import { account } from '$lib/appwrite';
  import { user } from '$lib/stores/auth';

  let debugInfo: any = {};
  let loading = true;
  let testEmailSent = false;

  onMount(async () => {
    try {
      const currentUser = await account.get();
      debugInfo = {
        user: {
          id: currentUser.$id,
          email: currentUser.email,
          name: currentUser.name,
          emailVerification: currentUser.emailVerification,
          status: currentUser.status
        },
        appwriteConfig: {
          endpoint: import.meta.env.VITE_APPWRITE_ENDPOINT,
          projectId: import.meta.env.VITE_APPWRITE_PROJECT_ID
        },
        verificationUrl: window.location.origin + '/verify'
      };
    } catch (err: any) {
      debugInfo = {
        error: err.message,
        appwriteConfig: {
          endpoint: import.meta.env.VITE_APPWRITE_ENDPOINT,
          projectId: import.meta.env.VITE_APPWRITE_PROJECT_ID
        }
      };
    }
    loading = false;
  });

  async function sendTestVerification() {
    try {
      await account.createVerification(window.location.origin + '/verify');
      testEmailSent = true;
      alert('Test verification email sent!');
    } catch (err: any) {
      alert('Failed to send test email: ' + err.message);
    }
  }
</script>

<main class="p-8 max-w-4xl mx-auto min-h-screen bg-gradient-to-br from-white to-gray-100">
  <h1 class="text-3xl font-bold mb-6">Email Verification Debug</h1>
  
  {#if loading}
    <div class="text-center py-8">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto mb-4"></div>
      <p>Đang kiểm tra cấu hình...</p>
    </div>
  {:else}
    <div class="space-y-6">
      <!-- Debug Info -->
      <div class="bg-white p-6 rounded-lg shadow border">
        <h2 class="text-xl font-semibold mb-4">Debug Information</h2>
        <pre class="text-sm overflow-auto bg-gray-100 p-4 rounded">{JSON.stringify(debugInfo, null, 2)}</pre>
      </div>

      <!-- Test Actions -->
      <div class="bg-white p-6 rounded-lg shadow border">
        <h2 class="text-xl font-semibold mb-4">Test Actions</h2>
        <div class="space-y-4">
          <button 
            class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
            on:click={sendTestVerification}
          >
            Send Test Verification Email
          </button>
          
          {#if testEmailSent}
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
              Test email sent! Check your inbox and console logs.
            </div>
          {/if}
        </div>
      </div>

      <!-- Troubleshooting Guide -->
      <div class="bg-white p-6 rounded-lg shadow border">
        <h2 class="text-xl font-semibold mb-4">Troubleshooting Guide</h2>
        <div class="space-y-4 text-sm">
          <div>
            <h3 class="font-medium text-gray-900 mb-2">1. Check Appwrite Console Settings:</h3>
            <ul class="list-disc list-inside text-gray-700 space-y-1">
              <li>Go to Appwrite Console → Auth → Settings</li>
              <li>Make sure "Email Verification" is enabled</li>
              <li>Check if custom SMTP is configured (if using custom email)</li>
              <li>Verify the verification URL template</li>
            </ul>
          </div>
          
          <div>
            <h3 class="font-medium text-gray-900 mb-2">2. Check Email Provider:</h3>
            <ul class="list-disc list-inside text-gray-700 space-y-1">
              <li>Check spam/junk folder</li>
              <li>Verify email address is correct</li>
              <li>Check if email provider blocks automated emails</li>
            </ul>
          </div>
          
          <div>
            <h3 class="font-medium text-gray-900 mb-2">3. Check URL Configuration:</h3>
            <ul class="list-disc list-inside text-gray-700 space-y-1">
              <li>Verification URL: <code class="bg-gray-100 px-1 rounded">{window.location.origin}/verify</code></li>
              <li>Make sure this URL is accessible</li>
              <li>Check if URL parameters (userId, secret) are being passed correctly</li>
            </ul>
          </div>
          
          <div>
            <h3 class="font-medium text-gray-900 mb-2">4. Common Issues:</h3>
            <ul class="list-disc list-inside text-gray-700 space-y-1">
              <li>Verification links expire after a certain time</li>
              <li>Links can only be used once</li>
              <li>Make sure user is not already verified</li>
              <li>Check browser console for JavaScript errors</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Navigation -->
      <div class="flex gap-4">
        <a href="/auth" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition">
          Back to Auth
        </a>
        <a href="/verify" class="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition">
          Test Verify Page
        </a>
      </div>
    </div>
  {/if}
</main>
