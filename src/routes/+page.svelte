<script lang="ts">
  import { goto } from '$app/navigation';
  import { account } from '$lib/appwrite';

  async function handleStart() {
    try {
      const user = await account.get();
      if (user.emailVerification) {
        goto('/room');
      } else {
        goto('/auth');
      }
    } catch {
      goto('/auth');
    }
  }
</script>

<main class="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-white to-gray-100">
  <section class="w-full max-w-xl mx-auto p-10 bg-white rounded-2xl shadow-xl border border-gray-200 animate-fade-in text-center">
    <img src="/svelte.svg" alt="Svelte" class="mx-auto mb-6 w-16 h-16 opacity-70 grayscale" />
    <h1 class="text-4xl font-extrabold text-black mb-4">Chat Private 2P</h1>
    <p class="text-lg text-gray-700 mb-6">
      Dự án Chat <PERSON> b<PERSON><PERSON> mậ<PERSON>, sử dụng <span class="font-semibold text-black">SvelteKit</span> + <span class="font-semibold text-black">Appwrite</span>.<br/>
      Đăng ký, xác thực email, quên mật khẩu, chat realtime, UI tối giản với Tailwind CSS.<br/>
      <span class="text-sm text-gray-400">(Demo: chỉ dành cho mục đích học tập, không lưu trữ dữ liệu nhạy cảm)</span>
    </p>
    <button class="px-8 py-3 cursor-pointer rounded-lg bg-black hover:bg-gray-900 text-white font-bold text-lg shadow transition" on:click={handleStart}>
      Bắt đầu Chat / Đăng nhập
    </button>
    <div class="mt-8 text-gray-400 text-xs">
      © 2025 Chat Private 2P. Powered by SvelteKit & Appwrite.
    </div>
  </section>
</main>
