<script lang="ts">
import { onMount } from 'svelte';
import { account, databases } from '$lib/appwrite';
import { writable } from 'svelte/store';

const databaseId = '686a3883000ac5847c3d';
const roomsCollectionId = '686a38b00033c187fde6';

let debugInfo = writable<any>({});
let loading = writable(true);

onMount(async () => {
  const info: any = {
    timestamp: new Date().toISOString(),
    appwriteConfig: {
      endpoint: import.meta.env.VITE_APPWRITE_ENDPOINT || 'Not set',
      projectId: import.meta.env.VITE_APPWRITE_PROJECT_ID || 'Not set'
    }
  };

  try {
    // Test account
    const user = await account.get();
    info.user = {
      id: user.$id,
      email: user.email,
      name: user.name,
      emailVerified: user.emailVerification,
      status: user.status
    };
  } catch (err: any) {
    info.userError = {
      message: err.message,
      code: err.code,
      type: err.type
    };
  }

  try {
    // Test database access
    const db = await databases.get(databaseId);
    info.database = {
      id: db.$id,
      name: db.name,
      enabled: db.enabled
    };
  } catch (err: any) {
    info.databaseError = {
      message: err.message,
      code: err.code,
      type: err.type
    };
  }

  try {
    // Test collection access
    const collection = await databases.getCollection(databaseId, roomsCollectionId);
    info.collection = {
      id: collection.$id,
      name: collection.name,
      enabled: collection.enabled,
      documentSecurity: collection.documentSecurity,
      attributes: collection.attributes?.length || 0
    };
  } catch (err: any) {
    info.collectionError = {
      message: err.message,
      code: err.code,
      type: err.type
    };
  }

  try {
    // Test listing documents
    const docs = await databases.listDocuments(databaseId, roomsCollectionId);
    info.documents = {
      total: docs.total,
      count: docs.documents.length
    };
  } catch (err: any) {
    info.documentsError = {
      message: err.message,
      code: err.code,
      type: err.type
    };
  }

  debugInfo.set(info);
  loading.set(false);
});
</script>

<main class="p-8 max-w-4xl mx-auto">
  <h1 class="text-3xl font-bold mb-6">Debug Information</h1>
  
  {#if $loading}
    <div class="text-center py-8">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto mb-4"></div>
      <p>Đang kiểm tra cấu hình...</p>
    </div>
  {:else}
    <div class="bg-gray-100 p-6 rounded-lg">
      <pre class="text-sm overflow-auto">{JSON.stringify($debugInfo, null, 2)}</pre>
    </div>
    
    <div class="mt-6 space-y-4">
      <h2 class="text-xl font-semibold">Hướng dẫn sửa lỗi:</h2>
      
      {#if $debugInfo.userError}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <strong>Lỗi User:</strong> {$debugInfo.userError.message}
          <br><small>Vui lòng đăng nhập lại</small>
        </div>
      {/if}
      
      {#if $debugInfo.databaseError}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <strong>Lỗi Database:</strong> {$debugInfo.databaseError.message}
          <br><small>Kiểm tra Database ID: {databaseId}</small>
        </div>
      {/if}
      
      {#if $debugInfo.collectionError}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <strong>Lỗi Collection:</strong> {$debugInfo.collectionError.message}
          <br><small>Kiểm tra Collection ID: {roomsCollectionId}</small>
        </div>
      {/if}
      
      {#if $debugInfo.documentsError}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <strong>Lỗi Documents:</strong> {$debugInfo.documentsError.message}
          <br><small>Kiểm tra permissions của collection</small>
        </div>
      {/if}
      
      {#if !$debugInfo.userError && !$debugInfo.databaseError && !$debugInfo.collectionError && !$debugInfo.documentsError}
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          <strong>✅ Tất cả đều hoạt động bình thường!</strong>
        </div>
      {/if}
    </div>
    
    <div class="mt-6">
      <a href="/room" class="px-4 py-2 bg-black text-white rounded hover:bg-gray-900 transition">
        Quay lại trang chính
      </a>
    </div>
  {/if}
</main>
