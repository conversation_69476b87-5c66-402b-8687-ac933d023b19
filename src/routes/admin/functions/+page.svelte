<script lang="ts">
import { onMount } from 'svelte';
import { user, isAuthenticated } from '$lib/stores/auth';
import { goto } from '$app/navigation';
import { writable } from 'svelte/store';
import { account, functions } from '$lib/appwrite';
import { checkFunctionsAvailability, validateRoomPassword } from '$lib/utils/roomFunctions';

// State
let functionsAvailable = writable(false);
let functionsList = writable<any[]>([]);
let loading = writable(true);
let error = writable('');

// Test state
let testRoomId = '';
let testPassword = '';
let testUserId = '';
let testResult = writable<any>(null);
let testLoading = writable(false);

onMount(async () => {
  // Check authentication
  try {
    const acc = await account.get();
    user.set({
      id: acc.$id,
      email: acc.email,
      displayName: acc.name,
      avatarUrl: acc.prefs?.avatarUrl || '',
      emailVerified: acc.emailVerification
    });
    isAuthenticated.set(true);
    testUserId = acc.$id;
  } catch {
    user.set(null);
    isAuthenticated.set(false);
    goto('/auth');
    return;
  }

  // Check functions availability
  await checkFunctions();
});

async function checkFunctions() {
  loading.set(true);
  error.set('');
  
  try {
    const available = await checkFunctionsAvailability();
    functionsAvailable.set(available);
    
    if (available) {
      // Try to list functions - note: this might not work without proper API key
      try {
        // For now, we'll use a placeholder since listing functions requires server API key
        functionsList.set([
          { $id: 'validate-room-password', name: 'Validate Room Password', runtime: 'node-18.0', enabled: false },
          { $id: 'grant-message-permissions', name: 'Grant Message Permissions', runtime: 'node-18.0', enabled: false }
        ]);
      } catch (err) {
        console.log('Cannot list functions:', err);
        functionsList.set([]);
      }
    } else {
      functionsList.set([]);
    }
  } catch (err: any) {
    console.error('Error checking functions:', err);
    error.set(`Lỗi kiểm tra Functions: ${err.message}`);
    functionsAvailable.set(false);
    functionsList.set([]);
  } finally {
    loading.set(false);
  }
}

async function testPasswordValidation() {
  if (!testRoomId || !testPassword || !testUserId) {
    alert('Vui lòng nhập đầy đủ thông tin test');
    return;
  }

  testLoading.set(true);
  testResult.set(null);
  
  try {
    const result = await validateRoomPassword(testRoomId, testPassword, testUserId);
    testResult.set(result);
  } catch (err: any) {
    testResult.set({
      success: false,
      error: err.message
    });
  } finally {
    testLoading.set(false);
  }
}

async function handleLogout() {
  try {
    await account.deleteSession('current');
  } catch {}
  user.set(null);
  isAuthenticated.set(false);
  goto('/auth');
}
</script>

<main class="flex flex-col items-center p-5 min-h-screen bg-gradient-to-br from-white to-gray-100">
  <section class="w-full max-w-4xl mx-auto mt-10 mb-8 p-8 bg-white rounded-2xl shadow-xl border border-gray-200 animate-fade-in">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
      <h1 class="text-3xl font-bold text-black">Appwrite Functions Admin</h1>
      <div class="flex items-center gap-4">
        <a href="/room" class="text-black hover:text-gray-700 underline">← Quay lại</a>
        {#if $user}
          <div class="flex items-center gap-2">
            <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 font-bold border border-gray-300">
              {($user.displayName || $user.email)?.[0]?.toUpperCase()}
            </div>
            <div class="flex flex-col leading-tight">
              <span class="text-sm font-semibold text-black">{$user.displayName || 'Không tên'}</span>
              <span class="text-xs text-gray-400">{$user.email}</span>
            </div>
          </div>
        {/if}
        <button class="text-sm text-black cursor-pointer underline hover:text-gray-700" on:click={handleLogout}>Đăng xuất</button>
      </div>
    </div>

    <!-- Functions Status -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold mb-4">Functions Status</h2>
      
      {#if $loading}
        <div class="text-center py-8">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto mb-4"></div>
          <h3 class="text-lg font-semibold text-black mb-2">Đang kiểm tra Functions...</h3>
          <p class="text-gray-600">Vui lòng chờ trong giây lát</p>
        </div>
      {:else if $error}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {$error}
        </div>
        <button 
          class="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-900 transition"
          on:click={checkFunctions}
        >
          Thử lại
        </button>
      {:else}
        <div class="space-y-4">
          <div class="flex items-center gap-4">
            <div class="flex items-center gap-2">
              <div class="w-3 h-3 rounded-full {$functionsAvailable ? 'bg-green-500' : 'bg-red-500'}"></div>
              <span class="font-medium">
                Functions Service: {$functionsAvailable ? 'Available' : 'Not Available'}
              </span>
            </div>
            <button 
              class="px-3 py-1 bg-gray-200 text-black rounded text-sm hover:bg-gray-300 transition"
              on:click={checkFunctions}
            >
              Refresh
            </button>
          </div>
          
          {#if $functionsAvailable}
            <div>
              <h3 class="font-medium mb-2">Available Functions ({$functionsList.length}):</h3>
              {#if $functionsList.length === 0}
                <p class="text-gray-500">Chưa có Functions nào được tạo.</p>
              {:else}
                <div class="space-y-2">
                  {#each $functionsList as func}
                    <div class="border border-gray-200 rounded p-3 bg-gray-50">
                      <div class="flex items-center justify-between">
                        <div>
                          <div class="font-medium">{func.name}</div>
                          <div class="text-sm text-gray-500">ID: {func.$id}</div>
                          <div class="text-sm text-gray-500">Runtime: {func.runtime}</div>
                        </div>
                        <div class="text-sm">
                          <span class="px-2 py-1 bg-{func.enabled ? 'green' : 'red'}-100 text-{func.enabled ? 'green' : 'red'}-800 rounded-full">
                            {func.enabled ? 'Enabled' : 'Disabled'}
                          </span>
                        </div>
                      </div>
                    </div>
                  {/each}
                </div>
              {/if}
            </div>
          {:else}
            <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
              <p class="font-medium">Functions không khả dụng</p>
              <p class="text-sm mt-1">
                Ứng dụng sẽ sử dụng fallback methods (kém bảo mật hơn). 
                Để sử dụng Functions, vui lòng:
              </p>
              <ul class="text-sm mt-2 list-disc list-inside">
                <li>Tạo Functions trong Appwrite Console</li>
                <li>Deploy code theo hướng dẫn trong docs/appwrite-functions.md</li>
                <li>Cấu hình environment variables</li>
              </ul>
            </div>
          {/if}
        </div>
      {/if}
    </div>

    <!-- Test Functions -->
    {#if $functionsAvailable}
      <div class="mb-8">
        <h2 class="text-xl font-semibold mb-4">Test Password Validation Function</h2>
        
        <div class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label for="testRoomId" class="block text-sm font-medium text-black mb-2">Room ID</label>
              <input
                id="testRoomId"
                type="text"
                bind:value={testRoomId}
                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black transition"
                placeholder="Nhập Room ID..."
              />
            </div>
            <div>
              <label for="testPassword" class="block text-sm font-medium text-black mb-2">Password</label>
              <input
                id="testPassword"
                type="password"
                bind:value={testPassword}
                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black transition"
                placeholder="Nhập password..."
              />
            </div>
            <div>
              <label for="testUserId" class="block text-sm font-medium text-black mb-2">User ID</label>
              <input
                id="testUserId"
                type="text"
                bind:value={testUserId}
                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black transition"
                placeholder="User ID..."
                readonly
              />
            </div>
          </div>
          
          <button
            class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition font-medium disabled:opacity-50"
            on:click={testPasswordValidation}
            disabled={$testLoading}
          >
            {$testLoading ? 'Testing...' : 'Test Function'}
          </button>
          
          {#if $testResult}
            <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
              <h4 class="font-medium mb-2">Test Result:</h4>
              <pre class="text-sm overflow-auto">{JSON.stringify($testResult, null, 2)}</pre>
            </div>
          {/if}
        </div>
      </div>
    {/if}

    <!-- Setup Instructions -->
    <div>
      <h2 class="text-xl font-semibold mb-4">Setup Instructions</h2>
      <div class="bg-gray-100 rounded-lg p-6">
        <p class="mb-4">Để setup Appwrite Functions cho room permissions:</p>
        <ol class="list-decimal list-inside space-y-2 text-sm">
          <li>Đọc hướng dẫn chi tiết trong <code class="bg-gray-200 px-2 py-1 rounded">docs/appwrite-functions.md</code></li>
          <li>Tạo API Key trong Appwrite Console với scopes: databases.read, databases.write, users.read</li>
          <li>Tạo Functions trong Appwrite Console với runtime Node.js 18</li>
          <li>Upload code và cấu hình environment variables</li>
          <li>Test Functions bằng form ở trên</li>
        </ol>
        <div class="mt-4">
          <a 
            href="/debug" 
            class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition text-sm"
          >
            Debug Page
          </a>
        </div>
      </div>
    </div>
  </section>
</main>
