<script lang="ts">
import { onMount } from 'svelte';
import { user, isAuthenticated } from '$lib/stores/auth';
import { goto } from '$app/navigation';
import { writable, get } from 'svelte/store';
import { account, client, databases } from '$lib/appwrite';
import { ID, Query, type Models, type RealtimeResponseEvent } from 'appwrite';

// Room list (fetch từ Appwrite DB)
const databaseId = '686a3883000ac5847c3d'; // Đổi lại đúng databaseId của bạn
const collectionId = '686a38b00033c187fde6'; // Đ<PERSON>i lại đúng collectionId của bạn
const messagesCollectionId = 'messages'; // Đổi lại đúng collectionId của bạn

let rooms = writable<{ id: string; name: string }[]>([]);
let roomsLoading = writable(true);
let roomsError = writable('');

let currentRoom = '';
let messages = writable<{ id: string; sender: string; text: string; time: string }[]>([]);
let messagesLoading = writable(false);
let messagesError = writable('');
let input = '';

onMount(async () => {
  // Luôn kiểm tra session thực tế với Appwrite
  try {
    const acc = await account.get();
    user.set({
      id: acc.$id,
      email: acc.email,
      displayName: acc.name,
      avatarUrl: acc.prefs?.avatarUrl || '',
      emailVerified: acc.emailVerification
    });
    isAuthenticated.set(true);
  } catch {
    user.set(null);
    isAuthenticated.set(false);
    goto('/auth');
    return;
  }
  // Fetch rooms từ Appwrite
  roomsLoading.set(true);
  roomsError.set('');
  try {
    const res = await databases.listDocuments(databaseId, collectionId);
    const fetchedRooms = res.documents.map((doc: any) => ({ id: doc.$id, name: doc.name }));
    rooms.set(fetchedRooms);
    if (fetchedRooms.length > 0) {
      currentRoom = fetchedRooms[0].id;
    } else {
      currentRoom = '';
    }
  } catch (err) {
    roomsError.set('Không thể tải danh sách phòng.');
    rooms.set([]);
    currentRoom = '';
  } finally {
    roomsLoading.set(false);
  }
  // Reset messages khi đổi room
  messages.set([]);
  if (currentRoom) fetchMessages(currentRoom);
});

async function fetchMessages(roomId: string) {
  messagesLoading.set(true);
  messagesError.set('');
  try {
    const res = await databases.listDocuments(databaseId, messagesCollectionId, [
      Query.equal('roomId', roomId),
      Query.orderDesc('$createdAt'),
      Query.limit(50)
    ]);
    const fetched = res.documents.map((doc: any) => ({
      id: doc.$id,
      sender: doc.senderName,
      text: doc.text,
      time: new Date(doc.$createdAt).toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })
    })).reverse();
    messages.set(fetched);
  } catch (err) {
    messagesError.set('Không thể tải tin nhắn.');
    messages.set([]);
  } finally {
    messagesLoading.set(false);
  }
}

function selectRoom(id: string) {
  currentRoom = id;
  fetchMessages(id);
}

async function sendMessage() {
  if (!input.trim() || !$user || !currentRoom) return;
  const senderName = $user.displayName || $user.email || 'Bạn';
  try {
    await databases.createDocument(databaseId, messagesCollectionId, ID.unique(), {
      roomId: currentRoom,
      senderName,
      text: input
    });
    input = '';
    // fetchMessages(currentRoom); // Không cần nếu có realtime
  } catch (err) {
    // Có thể thêm thông báo lỗi
  }
}

// Realtime subscribe messages
let unsubscribe: (() => void) | null = null;

$: if (currentRoom) {
  if (unsubscribe) unsubscribe();
  unsubscribe = client.subscribe(
    [`databases.${databaseId}.collections.${messagesCollectionId}.documents`],
    (event: RealtimeResponseEvent<Models.Document>) => {
      if (event.events.some(e => e.includes('databases.*.collections.*.documents.*.create')) && event.payload.roomId === currentRoom) {
        messages.update(msgs => [
          ...msgs,
          {
            id: event.payload.$id,
            sender: event.payload.senderName,
            text: event.payload.text,
            time: new Date(event.payload.$createdAt).toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })
          }
        ]);
      }
    }
  );
}

async function handleLogout() {
  try {
    await account.deleteSession('current');
  } catch {}
  user.set(null);
  isAuthenticated.set(false);
  goto('/auth');
}
</script>

<main class="flex flex-col items-center p-5 min-h-screen bg-gradient-to-br from-white to-gray-100">
  <section class="w-full max-w-2xl mx-auto mt-10 mb-8 p-0 bg-white rounded-2xl shadow-xl border border-gray-200 animate-fade-in flex overflow-hidden">
    <!-- Sidebar rooms -->
    <aside class="w-48 border-r border-gray-200 bg-gray-50 flex flex-col">
      <h2 class="text-lg font-bold text-black px-6 py-4 border-b border-gray-200">Phòng</h2>
      {#if $roomsLoading}
        <div class="px-6 py-4 text-gray-400">Đang tải...</div>
      {:else if $roomsError}
        <div class="px-6 py-4 text-red-500">{$roomsError}</div>
      {:else}
        <ul class="flex-1">
          {#each $rooms as room}
            <li>
              <button class="w-full text-left px-6 py-3 hover:bg-gray-100 focus:bg-gray-200 transition font-medium text-black {currentRoom === room.id ? 'bg-gray-200' : ''}" on:click={() => selectRoom(room.id)}>{room.name}</button>
            </li>
          {/each}
        </ul>
        {#if $rooms.length === 0}
          <div class="px-6 py-4 text-gray-400">Chưa có phòng nào.</div>
        {/if}
      {/if}
    </aside>
    <!-- Chat area -->
    <div class="flex-1 flex flex-col">
      <div class="flex items-center justify-between px-6 py-4 border-b border-gray-200">
        <h3 class="text-xl font-bold text-black">{$rooms.find(r => r.id === currentRoom)?.name}</h3>
        <div class="flex items-center gap-4">
          {#if $user}
            <div class="flex items-center gap-2">
              {#if $user.avatarUrl}
                <img src="{$user.avatarUrl}" alt="avatar" class="w-8 h-8 rounded-full border border-gray-300 bg-gray-100 object-cover" />
              {:else}
                <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 font-bold border border-gray-300">
                  {($user.displayName || $user.email)?.[0]?.toUpperCase()}
                </div>
              {/if}
              <div class="flex flex-col leading-tight">
                <span class="text-sm font-semibold text-black">{$user.displayName || 'Không tên'}</span>
                <span class="text-xs text-gray-400">{$user.email}</span>
              </div>
            </div>
          {/if}
          <button class="text-sm text-black cursor-pointer underline hover:text-gray-700" on:click={handleLogout}>Đăng xuất</button>
        </div>
      </div>
      <div class="flex-1 overflow-y-auto px-6 py-4 bg-white">
        {#if $messagesLoading}
          <div class="text-gray-400 text-center mt-10">Đang tải tin nhắn...</div>
        {:else if $messagesError}
          <div class="text-red-500 text-center mt-10">{$messagesError}</div>
        {:else if $messages.length === 0}
          <div class="text-gray-400 text-center mt-10">Chưa có tin nhắn nào.</div>
        {/if}
        {#each $messages as msg}
          <div class="mb-3">
            <span class="font-semibold text-black">{msg.sender}</span>
            <span class="mx-2 text-gray-400 text-xs">{msg.time}</span>
            <div class="ml-2 text-black">{msg.text}</div>
          </div>
        {/each}
      </div>
      <form class="flex gap-2 border-t border-gray-200 p-4 bg-white" on:submit|preventDefault={sendMessage}>
        <input
          class="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black transition bg-white text-black"
          type="text"
          placeholder="Nhập tin nhắn..."
          bind:value={input}
          autocomplete="off"
        />
        <button class="px-5 py-2 rounded-lg bg-black hover:bg-gray-900 text-white font-semibold transition" type="submit">Gửi</button>
      </form>
    </div>
  </section>
</main>
