<script lang="ts">
import { onMount } from 'svelte';
import { user, isAuthenticated } from '$lib/stores/auth';
import { goto } from '$app/navigation';
import { writable, get } from 'svelte/store';
import { account, databases } from '$lib/appwrite';
import { createSampleRooms } from '$lib/utils/sampleData';
import { validateRoomPassword, validateRoomPasswordFallback, checkFunctionsAvailability } from '$lib/utils/roomFunctions';

// Room list (fetch từ Appwrite DB)
const databaseId = '686a3883000ac5847c3d'; // Đ<PERSON>i lại đúng databaseId của bạn
const collectionId = '686a38b00033c187fde6'; // Đổi lại đúng collectionId của bạn

interface Room {
  id: string;
  name: string;
  isGroup: boolean;
  passwordProtected: boolean;
}

let rooms = writable<Room[]>([]);
let roomsLoading = writable(true);
let roomsError = writable('');

// Password modal state
let showPasswordModal = false;
let selectedRoomId = '';
let selectedRoomName = '';
let roomPassword = '';
let passwordError = '';

onMount(async () => {
  // Luôn kiểm tra session thực tế với Appwrite
  try {
    const acc = await account.get();
    user.set({
      id: acc.$id,
      email: acc.email,
      displayName: acc.name,
      avatarUrl: acc.prefs?.avatarUrl || '',
      emailVerified: acc.emailVerification
    });
    isAuthenticated.set(true);
  } catch {
    user.set(null);
    isAuthenticated.set(false);
    goto('/auth');
    return;
  }

  // Fetch rooms từ Appwrite
  await loadRooms();
});

async function loadRooms() {
  roomsLoading.set(true);
  roomsError.set('');
  try {
    const res = await databases.listDocuments(databaseId, collectionId);
    const fetchedRooms = res.documents.map((doc: any) => ({
      id: doc.$id,
      name: doc.name,
      isGroup: doc.isGroup || false,
      passwordProtected: doc.passwordProtected || true
    }));
    rooms.set(fetchedRooms);
  } catch (err: any) {
    console.error('Error loading rooms:', err);
    if (err.code === 401) {
      // Use fallback data when unauthorized
      roomsError.set('⚠️ Collection permissions chưa được cấu hình. Sử dụng dữ liệu mẫu.');
      const fallbackRooms = [
        {
          id: 'demo-room-1',
          name: 'Phòng Demo 1',
          isGroup: false,
          passwordProtected: true
        },
        {
          id: 'demo-room-2',
          name: 'Phòng Demo 2 (Nhóm)',
          isGroup: true,
          passwordProtected: true
        }
      ];
      rooms.set(fallbackRooms);
    } else if (err.code === 404) {
      roomsError.set('❌ Database hoặc collection không tồn tại. Vui lòng tạo collection trong Appwrite Console.');
      rooms.set([]);
    } else {
      roomsError.set(`Không thể tải danh sách phòng: ${err.message || 'Lỗi không xác định'}`);
      rooms.set([]);
    }
  } finally {
    roomsLoading.set(false);
  }
}

function showPasswordPrompt(roomId: string, roomName: string) {
  selectedRoomId = roomId;
  selectedRoomName = roomName;
  roomPassword = '';
  passwordError = '';
  showPasswordModal = true;
}

function closePasswordModal() {
  showPasswordModal = false;
  selectedRoomId = '';
  selectedRoomName = '';
  roomPassword = '';
  passwordError = '';
}

async function joinRoomWithPassword() {
  if (!roomPassword.trim()) {
    passwordError = 'Vui lòng nhập mật khẩu';
    return;
  }

  const currentUser = get(user);
  if (!currentUser) {
    passwordError = 'Người dùng chưa đăng nhập';
    return;
  }

  try {
    // Check if Functions are available
    const functionsAvailable = await checkFunctionsAvailability();

    let result;
    if (functionsAvailable) {
      // Use secure server-side validation
      result = await validateRoomPassword(selectedRoomId, roomPassword, currentUser.id);
    } else {
      // Fallback to client-side validation (less secure)
      result = await validateRoomPasswordFallback(
        selectedRoomId,
        roomPassword,
        databases,
        databaseId,
        collectionId
      );
    }

    if (result.success) {
      // Password is correct, join the room
      closePasswordModal();
      goto(`/chat/${selectedRoomId}`);
    } else {
      passwordError = result.error || 'Mật khẩu không đúng';
    }
  } catch (err: any) {
    console.error('Error joining room:', err);
    passwordError = `Không thể tham gia phòng: ${err.message}`;
  }
}

async function handleCreateSampleData() {
  if (!$user) return;

  try {
    roomsLoading.set(true);
    await createSampleRooms($user.id);
    await loadRooms();
  } catch (err: any) {
    console.error('Error creating sample data:', err);
    roomsError.set(`Không thể tạo dữ liệu mẫu: ${err.message}`);
  }
}

async function handleLogout() {
  try {
    await account.deleteSession('current');
  } catch {}
  user.set(null);
  isAuthenticated.set(false);
  goto('/auth');
}
</script>

<main class="flex flex-col items-center p-5 min-h-screen bg-gradient-to-br from-white to-gray-100">
  <section class="w-full max-w-4xl mx-auto mt-10 mb-8 p-8 bg-white rounded-2xl shadow-xl border border-gray-200 animate-fade-in">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
      <h1 class="text-3xl font-bold text-black">Danh sách phòng chat</h1>
      <div class="flex items-center gap-4">
        <a href="/friends" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition font-medium">
          👥 Bạn bè
        </a>
        <a href="/admin/functions" class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition font-medium">
          ⚙️ Functions
        </a>
        {#if $user}
          <div class="flex items-center gap-2">
            {#if $user.avatarUrl}
              <img src="{$user.avatarUrl}" alt="avatar" class="w-8 h-8 rounded-full border border-gray-300 bg-gray-100 object-cover" />
            {:else}
              <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 font-bold border border-gray-300">
                {($user.displayName || $user.email)?.[0]?.toUpperCase()}
              </div>
            {/if}
            <div class="flex flex-col leading-tight">
              <span class="text-sm font-semibold text-black">{$user.displayName || 'Không tên'}</span>
              <span class="text-xs text-gray-400">{$user.email}</span>
            </div>
          </div>
        {/if}
        <button class="text-sm text-black cursor-pointer underline hover:text-gray-700" on:click={handleLogout}>Đăng xuất</button>
      </div>
    </div>

    <!-- Room List -->
    {#if $roomsLoading}
      <div class="text-center py-12">
        <div class="text-gray-400">Đang tải danh sách phòng...</div>
      </div>
    {:else if $roomsError}
      <div class="text-center py-12">
        <div class="text-red-500 mb-4">{$roomsError}</div>

        {#if $roomsError.includes('permissions')}
          <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-6 py-4 rounded-lg mb-6 text-left max-w-2xl mx-auto">
            <h3 class="font-bold mb-2">🔧 Cách sửa lỗi Permissions:</h3>
            <ol class="list-decimal list-inside space-y-1 text-sm">
              <li>Vào <strong>Appwrite Console</strong> → <strong>Databases</strong></li>
              <li>Chọn Database ID: <code class="bg-yellow-200 px-1 rounded">686a3883000ac5847c3d</code></li>
              <li>Chọn Collection ID: <code class="bg-yellow-200 px-1 rounded">686a38b00033c187fde6</code></li>
              <li>Vào tab <strong>Settings</strong> → <strong>Permissions</strong></li>
              <li>Thêm <strong>Read permission</strong>: <code class="bg-yellow-200 px-1 rounded">users</code></li>
              <li>Thêm <strong>Write permission</strong>: <code class="bg-yellow-200 px-1 rounded">users</code></li>
            </ol>
            <div class="mt-3">
              <a href="/debug" class="text-blue-600 hover:underline text-sm">→ Xem Debug Page để kiểm tra chi tiết</a>
            </div>
          </div>
        {/if}

        {#if $roomsError.includes('không tồn tại')}
          <div class="bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-6 text-left max-w-2xl mx-auto">
            <h3 class="font-bold mb-2">❌ Collection chưa được tạo:</h3>
            <ol class="list-decimal list-inside space-y-1 text-sm">
              <li>Vào <strong>Appwrite Console</strong> → <strong>Databases</strong></li>
              <li>Tạo Collection với ID: <code class="bg-red-200 px-1 rounded">686a38b00033c187fde6</code></li>
              <li>Tên Collection: <code class="bg-red-200 px-1 rounded">rooms</code></li>
              <li>Thêm các attributes: name, isGroup, passwordProtected, password, createdBy, members</li>
            </ol>
          </div>
        {/if}

        <div class="flex gap-4 justify-center">
          <button class="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-900 transition" on:click={loadRooms}>
            Thử lại
          </button>
          <a href="/admin/functions" class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition">
            Functions Admin
          </a>
        </div>
      </div>
    {:else if $rooms.length === 0}
      <div class="text-center py-12">
        <div class="text-gray-400 mb-4">Chưa có phòng chat nào.</div>
        <div class="flex gap-4 justify-center">
          <button
            class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition font-semibold"
            on:click={handleCreateSampleData}
          >
            Tạo dữ liệu mẫu
          </button>
          <a href="/room/create" class="px-6 py-3 bg-black text-white rounded-lg hover:bg-gray-900 transition font-semibold">
            Tạo phòng mới
          </a>
        </div>
      </div>
    {:else}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {#each $rooms as room}
          <div class="border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow bg-gray-50">
            <div class="flex items-start justify-between mb-4">
              <h3 class="text-lg font-semibold text-black">{room.name}</h3>
              <div class="flex gap-2">
                {#if room.isGroup}
                  <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">Nhóm</span>
                {:else}
                  <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">1-1</span>
                {/if}
                {#if room.passwordProtected}
                  <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">🔒</span>
                {/if}
              </div>
            </div>
            <button
              class="w-full px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-900 transition font-medium"
              on:click={() => showPasswordPrompt(room.id, room.name)}
            >
              Tham gia phòng
            </button>
          </div>
        {/each}
      </div>

      <!-- Create Room Button -->
      <div class="mt-8 text-center">
        <a href="/room/create" class="px-6 py-3 bg-black text-white rounded-lg hover:bg-gray-900 transition font-semibold">
          + Tạo phòng mới
        </a>
      </div>
    {/if}
  </section>

  <!-- Password Modal -->
  {#if showPasswordModal}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
        <h3 class="text-xl font-bold text-black mb-4">Nhập mật khẩu phòng</h3>
        <p class="text-gray-600 mb-6">Phòng "{selectedRoomName}" yêu cầu mật khẩu để tham gia.</p>

        <form on:submit|preventDefault={joinRoomWithPassword} class="space-y-4">
          <div>
            <label for="password" class="block text-sm font-medium text-black mb-2">Mật khẩu</label>
            <input
              id="password"
              type="password"
              bind:value={roomPassword}
              class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-black transition"
              placeholder="Nhập mật khẩu..."

            />
          </div>

          {#if passwordError}
            <div class="text-red-500 text-sm">{passwordError}</div>
          {/if}

          <div class="flex gap-4">
            <button
              type="submit"
              class="flex-1 py-2 bg-black text-white rounded-lg hover:bg-gray-900 transition font-medium"
            >
              Tham gia
            </button>
            <button
              type="button"
              on:click={closePasswordModal}
              class="px-6 py-2 border border-gray-300 text-black rounded-lg hover:bg-gray-50 transition font-medium"
            >
              Hủy
            </button>
          </div>
        </form>
      </div>
    </div>
  {/if}
</main>
