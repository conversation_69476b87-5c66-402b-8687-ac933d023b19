<script lang="ts">
import { onMount } from 'svelte';
import { user, isAuthenticated } from '$lib/stores/auth';
import { goto } from '$app/navigation';
import { writable, get } from 'svelte/store';
import { account, databases } from '$lib/appwrite';
import { validateRoomPassword, validateRoomPasswordFallback, checkFunctionsAvailability } from '$lib/utils/roomFunctions';
import PasswordInput from '$lib/components/PasswordInput.svelte';
import { Query } from 'appwrite';
import { DATABASE_CONFIG } from '$lib/constants/database';
import { searchUsers, ensureUserProfile } from '$lib/utils/userProfile';

// Use centralized database config

interface Room {
  id: string;
  name: string;
  isGroup: boolean;
  passwordProtected: boolean;
}

interface User {
  id: string;
  email: string;
  displayName: string;
  avatarUrl?: string;
}

// Search state
let searchQuery = '';
let searchResults = writable<(Room | User)[]>([]);
let searchLoading = writable(false);
let searchError = writable('');
let searchType: 'rooms' | 'users' = 'rooms';

// Password modal state
let showPasswordModal = false;
let selectedRoomId = '';
let selectedRoomName = '';
let roomPassword = '';
let passwordError = '';

onMount(async () => {
  // Luôn kiểm tra session thực tế với Appwrite
  try {
    const acc = await account.get();

    // Ensure user profile exists in database for search functionality
    await ensureUserProfile(acc);

    user.set({
      id: acc.$id,
      email: acc.email,
      displayName: acc.name,
      avatarUrl: acc.prefs?.avatarUrl || '',
      emailVerified: acc.emailVerification
    });
    isAuthenticated.set(true);
  } catch {
    user.set(null);
    isAuthenticated.set(false);
    goto('/auth');
    return;
  }

  // No auto-loading of rooms - only search
});

// Search function for rooms and users
async function performSearch() {
  if (!searchQuery.trim()) {
    searchResults.set([]);
    return;
  }

  searchLoading.set(true);
  searchError.set('');

  try {
    let results: (Room | User)[] = [];

    if (searchType === 'rooms') {
      // Search rooms by name
      try {
        const roomResults = await databases.listDocuments(DATABASE_CONFIG.DATABASE_ID, DATABASE_CONFIG.COLLECTIONS.ROOMS, [
          Query.search('name', searchQuery)
        ]);
        results = roomResults.documents.map((doc: any) => ({
          id: doc.$id,
          name: doc.name,
          isGroup: doc.isGroup || false,
          passwordProtected: doc.passwordProtected || true
        }));
      } catch (err) {
        console.error('Room search failed:', err);
        searchError.set('Không thể tìm kiếm phòng. Vui lòng kiểm tra cấu hình database.');
      }
    } else {
      // Search users - use Appwrite's built-in users API instead of custom collection
      try {
        // Use Appwrite's built-in user search
        // Note: This requires proper permissions in Appwrite Console

        // For now, let's use a simple approach - search in our custom users collection
        // but handle the case where email attribute might not exist

        let searchResults: any[] = [];

        // Use utility function for user search
        try {
          console.log('=== USER SEARCH DEBUG ===');
          console.log('Search query:', searchQuery);

          const userResults = await searchUsers(searchQuery);
          console.log('User search results:', userResults.length);

          if (userResults.length === 0) {
            console.log('No users found!');
            searchError.set('Không tìm thấy người dùng nào.');
            return;
          }

          searchResults = userResults;
        } catch (simpleErr: any) {
          console.error('User search failed:', simpleErr);
          searchError.set(`Lỗi tìm kiếm: ${simpleErr.message}`);
        }



        // Remove duplicates and current user
        const currentUser = get(user);
        results = searchResults.filter((searchUser: any, index, self) =>
          index === self.findIndex((u: any) => u.$id === searchUser.$id) &&
          searchUser.$id !== currentUser?.id
        ).map((doc: any) => ({
          id: doc.$id,
          email: doc.userId || doc.displayName || 'No identifier',  // Use userId as email fallback
          displayName: doc.displayName || doc.name || 'No name',
          avatarUrl: doc.avatarUrl
        }));

      } catch (err: any) {
        console.error('User search failed:', err);

        // If all search methods fail, provide helpful error message
        if (err.message?.includes('Attribute not found')) {
          searchError.set('Tính năng tìm kiếm người dùng chưa được cấu hình đúng. Vui lòng kiểm tra database schema.');
        } else {
          searchError.set(`Không thể tìm kiếm người dùng: ${err.message}`);
        }
      }
    }

    searchResults.set(results);

    if (results.length === 0 && !get(searchError)) {
      searchError.set(`Không tìm thấy ${searchType === 'rooms' ? 'phòng' : 'người dùng'} nào.`);
    }
  } catch (err: any) {
    console.error('Search error:', err);
    searchError.set(`Lỗi tìm kiếm: ${err.message}`);
    searchResults.set([]);
  } finally {
    searchLoading.set(false);
  }
}

// Debounced search
let searchTimeout: NodeJS.Timeout;
function handleSearchInput() {
  clearTimeout(searchTimeout);
  if (searchQuery.trim()) {
    searchTimeout = setTimeout(performSearch, 300);
  } else {
    searchResults.set([]);
    searchError.set('');
  }
}

function showPasswordPrompt(roomId: string, roomName: string) {
  selectedRoomId = roomId;
  selectedRoomName = roomName;
  roomPassword = '';
  passwordError = '';
  showPasswordModal = true;
}

function closePasswordModal() {
  showPasswordModal = false;
  selectedRoomId = '';
  selectedRoomName = '';
  roomPassword = '';
  passwordError = '';
}

async function joinRoomWithPassword() {
  if (!roomPassword.trim()) {
    passwordError = 'Vui lòng nhập mật khẩu';
    return;
  }

  const currentUser = get(user);
  if (!currentUser) {
    passwordError = 'Người dùng chưa đăng nhập';
    return;
  }

  try {
    // Check if Functions are available
    const functionsAvailable = await checkFunctionsAvailability();

    let result: any;
    if (functionsAvailable) {
      // Use secure server-side validation
      result = await validateRoomPassword(selectedRoomId, roomPassword, currentUser.id);
    } else {
      // Fallback to client-side validation (less secure)
      result = await validateRoomPasswordFallback(
        selectedRoomId,
        roomPassword,
        databases,
        DATABASE_CONFIG.DATABASE_ID,
        DATABASE_CONFIG.COLLECTIONS.ROOMS
      );
    }

    if (result.success) {
      // Password is correct, join the room
      closePasswordModal();
      goto(`/chat/${selectedRoomId}`);
    } else {
      passwordError = result.error || 'Mật khẩu không đúng';
    }
  } catch (err: any) {
    console.error('Error joining room:', err);
    passwordError = `Không thể tham gia phòng: ${err.message}`;
  }
}

// Create sample users for testing
async function createSampleUsers() {
  const currentUser = get(user);
  if (!currentUser) return;

  try {
    const sampleUsers = [
      {
        userId: '<EMAIL>',
        displayName: 'Nguyễn Văn A',
        name: 'Nguyễn Văn A',
        email: '<EMAIL>',
        avatarUrl: ''
      },
      {
        userId: '<EMAIL>',
        displayName: 'Trần Thị B',
        name: 'Trần Thị B',
        email: '<EMAIL>',
        avatarUrl: ''
      },
      {
        userId: '<EMAIL>',
        displayName: 'Lê Văn C',
        name: 'Lê Văn C',
        email: '<EMAIL>',
        avatarUrl: ''
      }
    ];

    for (const userData of sampleUsers) {
      try {
        await databases.createDocument(DATABASE_CONFIG.DATABASE_ID, DATABASE_CONFIG.COLLECTIONS.USERS, 'unique()', userData);
        console.log('Created sample user:', userData.displayName);
      } catch (err) {
        console.log('User might already exist:', userData.displayName);
      }
    }

    alert('Đã tạo dữ liệu mẫu! Thử search lại.');
  } catch (err: any) {
    console.error('Error creating sample users:', err);
    alert(`Không thể tạo dữ liệu mẫu: ${err.message}`);
  }
}

// Add friend request function
async function sendFriendRequest(targetUserId: string) {
  const currentUser = get(user);
  if (!currentUser) return;

  try {
    // Check if request already exists
    const existingRequests = await databases.listDocuments(DATABASE_CONFIG.DATABASE_ID, DATABASE_CONFIG.COLLECTIONS.FRIENDS, [
      Query.equal('from', currentUser.id),
      Query.equal('to', targetUserId)
    ]);

    if (existingRequests.documents.length > 0) {
      alert('Bạn đã gửi lời mời kết bạn cho người này rồi.');
      return;
    }

    // Create friend request
    await databases.createDocument(DATABASE_CONFIG.DATABASE_ID, DATABASE_CONFIG.COLLECTIONS.FRIENDS, 'unique()', {
      from: currentUser.id,
      to: targetUserId,
      status: 'pending',
      createdAt: new Date().toISOString()
    });

    alert('Đã gửi lời mời kết bạn!');
  } catch (err: any) {
    console.error('Error sending friend request:', err);
    alert(`Không thể gửi lời mời: ${err.message}`);
  }
}

async function handleLogout() {
  try {
    await account.deleteSession('current');
  } catch {}
  user.set(null);
  isAuthenticated.set(false);
  goto('/auth');
}
</script>

<main class="flex h-screen bg-white">
  <!-- Sidebar -->
  <div class="w-full md:w-80 border-r border-gray-200 flex flex-col bg-white md:flex-shrink-0">
    <!-- Header -->
    <div class="p-4 border-b border-gray-200">
      <div class="flex items-center justify-between mb-4">
        <h1 class="text-2xl font-bold text-black">Chats</h1>
        <div class="flex items-center gap-2">
          <!-- New chat button -->
          <button class="p-2 hover:bg-gray-100 rounded-full transition-colors" aria-label="Tạo chat mới">
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
          </button>
          <!-- Settings button -->
          <button class="p-2 hover:bg-gray-100 rounded-full transition-colors" aria-label="Cài đặt">
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Search bar -->
      <div class="relative">
        <input
          type="text"
          placeholder={searchType === 'rooms' ? 'Tìm kiếm phòng chat...' : 'Tìm kiếm người dùng...'}
          bind:value={searchQuery}
          on:input={handleSearchInput}
          class="w-full bg-gray-100 border-0 rounded-full px-4 py-2 pl-10 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white transition"
        />
        <svg class="w-4 h-4 text-gray-500 absolute left-3 top-1/2 transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
      </div>

      <!-- Search type toggle -->
      <div class="flex bg-gray-100 rounded-lg p-1 mt-3">
        <button
          class="flex-1 py-2 px-3 text-sm font-medium rounded-md transition {searchType === 'rooms' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}"
          on:click={() => { searchType = 'rooms'; performSearch(); }}
        >
          Phòng chat
        </button>
        <button
          class="flex-1 py-2 px-3 text-sm font-medium rounded-md transition {searchType === 'users' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}"
          on:click={() => { searchType = 'users'; performSearch(); }}
        >
          Người dùng
        </button>
      </div>
    </div>

    <!-- Quick actions -->
    <div class="p-4 border-b border-gray-200">
      <div class="flex gap-2">
        <a href="/friends" class="flex-1 flex items-center gap-2 px-3 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition text-sm font-medium">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
          </svg>
          Bạn bè
        </a>
        <a href="/room/create" class="flex-1 flex items-center gap-2 px-3 py-2 bg-green-50 text-green-600 rounded-lg hover:bg-green-100 transition text-sm font-medium">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
          Tạo phòng
        </a>
      </div>
    </div>
    <!-- Search Results -->
    <div class="flex-1 overflow-y-auto">
      {#if $searchLoading}
        <div class="p-2">
          {#each Array(3) as _}
            <div class="flex items-center gap-3 p-3 mb-1 animate-pulse">
              <div class="w-14 h-14 bg-gray-200 rounded-full flex-shrink-0"></div>
              <div class="flex-1 min-w-0">
                <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div class="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          {/each}
        </div>
      {:else if $searchError}
        <div class="p-4 text-center">
          <div class="text-red-500 text-sm mb-3">{$searchError}</div>
        </div>
      {:else if !searchQuery.trim()}
        <div class="p-4 text-center">
          <div class="py-8">
            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Tìm kiếm để bắt đầu</h3>
            <p class="text-gray-500 text-sm">
              {#if searchType === 'rooms'}
                Nhập tên phòng để tìm kiếm và tham gia
              {:else}
                Nhập email hoặc tên để tìm kiếm người dùng
              {/if}
            </p>
          </div>
        </div>
      {:else if $searchResults.length === 0}
        <div class="p-4 text-center">
          <div class="py-8">
            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Không tìm thấy kết quả</h3>
            <p class="text-gray-500 text-sm mb-4">
              {#if searchType === 'rooms'}
                Thử tìm kiếm với tên phòng khác
              {:else}
                Thử tìm kiếm với tên hoặc email khác, hoặc tạo dữ liệu mẫu để test
              {/if}
            </p>
            {#if searchType === 'users'}
              <button
                class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition text-sm font-medium"
                on:click={createSampleUsers}
              >
                Tạo người dùng mẫu
              </button>
            {/if}
          </div>
        </div>
      {:else}
        <div class="p-2">
          {#each $searchResults as result}
            {#if searchType === 'rooms'}
              {@const room = result as Room}
              <button
                class="w-full flex items-center gap-3 p-3 hover:bg-gray-100 rounded-lg transition-colors text-left"
                on:click={() => showPasswordPrompt(room.id, room.name)}
              >
                <!-- Room Avatar -->
                <div class="relative flex-shrink-0">
                  <div class="w-14 h-14 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
                    <span class="text-white font-semibold text-lg">
                      {room.name[0]?.toUpperCase()}
                    </span>
                  </div>
                  {#if room.isGroup}
                    <div class="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                      <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                    </div>
                  {:else}
                    <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                  {/if}
                </div>

                <!-- Room info -->
                <div class="flex-1 min-w-0">
                  <div class="flex items-center justify-between mb-1">
                    <h3 class="font-semibold text-gray-900 truncate">{room.name}</h3>
                    {#if room.passwordProtected}
                      <svg class="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                      </svg>
                    {/if}
                  </div>
                  <p class="text-sm text-gray-600 truncate">
                    {room.isGroup ? 'Nhóm chat' : 'Chat riêng tư'} • Nhấn để tham gia
                  </p>
                </div>
              </button>
            {:else}
              {@const searchUser = result as User}
              <div class="flex items-center gap-3 p-3 hover:bg-gray-100 rounded-lg transition-colors">
                <!-- User Avatar -->
                <div class="w-14 h-14 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                  <span class="text-white font-semibold text-lg">
                    {(searchUser.displayName || searchUser.email)[0]?.toUpperCase()}
                  </span>
                </div>

                <!-- User info -->
                <div class="flex-1 min-w-0">
                  <h3 class="font-semibold text-gray-900 truncate">{searchUser.displayName || 'Không tên'}</h3>
                  <p class="text-sm text-gray-600 truncate">{searchUser.email}</p>
                </div>

                <!-- Action button -->
                <button
                  class="px-3 py-1.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition text-sm font-medium"
                  on:click={() => sendFriendRequest(searchUser.id)}
                >
                  Kết bạn
                </button>
              </div>
            {/if}
          {/each}
        </div>
      {/if}
    </div>
  </div>

  <!-- Main content area -->
  <div class="hidden md:flex flex-1 items-center justify-center bg-gray-50">
    <div class="text-center">
      <div class="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
        <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
        </svg>
      </div>
      <h2 class="text-2xl font-semibold text-gray-900 mb-2">Chọn một cuộc trò chuyện</h2>
      <p class="text-gray-500 mb-6">Chọn một cuộc trò chuyện từ danh sách bên trái để bắt đầu nhắn tin</p>

      <!-- User info -->
      {#if $user}
        <div class="flex items-center justify-center gap-3 mb-4">
          {#if $user.avatarUrl}
            <img src="{$user.avatarUrl}" alt="avatar" class="w-10 h-10 rounded-full border border-gray-300 bg-gray-100 object-cover" />
          {:else}
            <div class="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 font-bold border border-gray-300">
              {($user.displayName || $user.email)?.[0]?.toUpperCase()}
            </div>
          {/if}
          <div class="text-left">
            <div class="font-semibold text-gray-900">{$user.displayName || 'Không tên'}</div>
            <div class="text-sm text-gray-500">{$user.email}</div>
          </div>
        </div>
        <button
          class="text-sm text-blue-600 hover:text-blue-700 underline"
          on:click={handleLogout}
        >
          Đăng xuất
        </button>
      {/if}
    </div>
  </div>

  <!-- Password Modal -->
  {#if showPasswordModal}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-2xl p-6 max-w-sm w-full shadow-2xl animate-fade-in">
        <div class="text-center mb-6">
          <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">Nhập mật khẩu</h3>
          <p class="text-gray-600 text-sm">Phòng "{selectedRoomName}" yêu cầu mật khẩu để tham gia</p>
        </div>

        <form on:submit|preventDefault={joinRoomWithPassword} class="space-y-4">
          <div>
            <PasswordInput
              id="password"
              bind:value={roomPassword}
              placeholder="Nhập mật khẩu phòng..."
              showStrength={false}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 pr-12 focus:outline-none focus:ring-2 focus:ring-blue-500 transition bg-white text-black"
              required={true}
            />
          </div>

          {#if passwordError}
            <div class="bg-red-50 border border-red-200 text-red-600 px-3 py-2 rounded-lg text-sm">
              {passwordError}
            </div>
          {/if}

          <div class="flex gap-3">
            <button
              type="button"
              on:click={closePasswordModal}
              class="flex-1 py-2.5 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition font-medium"
            >
              Hủy
            </button>
            <button
              type="submit"
              class="flex-1 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition font-medium"
            >
              Tham gia
            </button>
          </div>
        </form>
      </div>
    </div>
  {/if}
</main>
