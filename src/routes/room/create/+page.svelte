<script lang="ts">
import { onMount } from 'svelte';
import { user, isAuthenticated } from '$lib/stores/auth';
import { goto } from '$app/navigation';
import { writable, get } from 'svelte/store';
import { account, databases } from '$lib/appwrite';
import { ID, Permission, Role } from 'appwrite';
import { z } from 'zod';
import { roomPasswordSchema } from '$lib/utils/passwordValidation';
import PasswordInput from '$lib/components/PasswordInput.svelte';
import { generateRandomPassword } from '$lib/utils/randomGenerator';

// Database config
const databaseId = '686a3883000ac5847c3d';
const roomsCollectionId = '686a38b00033c187fde6';
const friendRequestsCollectionId = 'friend_requests';
const usersCollectionId = 'users';

// Form state
let roomName = '';
let roomType: 'private' | 'group' = 'private';
let roomPassword = '';
let selectedFriends: string[] = [];

// UI state
let loading = writable(false);
let error = writable('');
let friends = writable<any[]>([]);
let friendsLoading = writable(true);



// Validation schema
const createRoomSchema = z.object({
  roomName: z.string()
    .min(1, 'Tên phòng không được để trống')
    .max(50, 'Tên phòng không được quá 50 ký tự')
    .refine((name) => name.trim().length > 0, 'Tên phòng không được chỉ chứa khoảng trắng'),
  roomPassword: roomPasswordSchema,
  roomType: z.enum(['private', 'group'])
});

onMount(async () => {
  // Check authentication
  try {
    const acc = await account.get();
    user.set({
      id: acc.$id,
      email: acc.email,
      displayName: acc.name,
      avatarUrl: acc.prefs?.avatarUrl || '',
      emailVerified: acc.emailVerification
    });
    isAuthenticated.set(true);
  } catch {
    user.set(null);
    isAuthenticated.set(false);
    goto('/auth');
    return;
  }

  // Load friends list
  await loadFriends();
});

async function loadFriends() {
  const currentUser = get(user);
  if (!currentUser) return;
  
  friendsLoading.set(true);
  
  try {
    // Load accepted friend requests where current user is involved
    const sentRequests = await databases.listDocuments(databaseId, friendRequestsCollectionId, [
      // Query.equal('from', currentUser.id),
      // Query.equal('status', 'accepted')
    ]);
    
    const receivedRequests = await databases.listDocuments(databaseId, friendRequestsCollectionId, [
      // Query.equal('to', currentUser.id),
      // Query.equal('status', 'accepted')
    ]);
    
    // Get friend IDs
    const friendIds = [
      ...sentRequests.documents.map((req: any) => req.to),
      ...receivedRequests.documents.map((req: any) => req.from)
    ];
    
    // Load friend profiles
    const friendProfiles = [];
    for (const friendId of friendIds) {
      try {
        const profile = await databases.getDocument(databaseId, usersCollectionId, friendId);
        friendProfiles.push(profile);
      } catch (err) {
        console.log(`Failed to load profile for ${friendId}:`, err);
      }
    }
    
    friends.set(friendProfiles);
  } catch (err: any) {
    console.error('Error loading friends:', err);
    friends.set([]);
  } finally {
    friendsLoading.set(false);
  }
}

async function createRoom() {
  const currentUser = get(user);
  if (!currentUser) return;

  // Validate form
  const validation = createRoomSchema.safeParse({
    roomName,
    roomPassword,
    roomType
  });

  if (!validation.success) {
    error.set(validation.error.errors[0].message);
    return;
  }

  // For private rooms, must have exactly one friend selected
  if (roomType === 'private' && selectedFriends.length !== 1) {
    error.set('Phòng riêng tư phải có đúng 1 bạn bè được chọn.');
    return;
  }

  // For group rooms, must have at least one friend selected
  if (roomType === 'group' && selectedFriends.length === 0) {
    error.set('Phòng nhóm phải có ít nhất 1 bạn bè được chọn.');
    return;
  }

  loading.set(true);
  error.set('');

  try {
    // Create room document
    const roomData = {
      name: roomName,
      isGroup: roomType === 'group',
      passwordProtected: true,
      password: roomPassword, // Note: In production, this should be hashed server-side
      createdBy: currentUser.id,
      members: [currentUser.id, ...selectedFriends],
      createdAt: new Date().toISOString()
    };

    const newRoom = await databases.createDocument(
      databaseId,
      roomsCollectionId,
      ID.unique(),
      roomData,
      [
        Permission.read(Role.user(currentUser.id)),
        Permission.update(Role.user(currentUser.id)),
        Permission.delete(Role.user(currentUser.id)),
        // Add read permissions for selected friends
        ...selectedFriends.map(friendId => Permission.read(Role.user(friendId)))
      ]
    );

    console.log('Room created:', newRoom);
    
    // Redirect to the new room
    goto(`/chat/${newRoom.$id}`);
  } catch (err: any) {
    console.error('Error creating room:', err);
    error.set(`Không thể tạo phòng: ${err.message}`);
  } finally {
    loading.set(false);
  }
}

function toggleFriendSelection(friendId: string) {
  if (roomType === 'private') {
    // For private rooms, only allow one friend
    selectedFriends = selectedFriends.includes(friendId) ? [] : [friendId];
  } else {
    // For group rooms, allow multiple friends
    if (selectedFriends.includes(friendId)) {
      selectedFriends = selectedFriends.filter(id => id !== friendId);
    } else {
      selectedFriends = [...selectedFriends, friendId];
    }
  }
}

async function handleLogout() {
  try {
    await account.deleteSession('current');
  } catch {}
  user.set(null);
  isAuthenticated.set(false);
  goto('/auth');
}
</script>

<main class="flex flex-col items-center p-5 min-h-screen bg-gradient-to-br from-white to-gray-100">
  <section class="w-full max-w-2xl mx-auto mt-10 mb-8 p-8 bg-white rounded-2xl shadow-xl border border-gray-200 animate-fade-in">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
      <h1 class="text-3xl font-bold text-black">Tạo phòng chat mới</h1>
      <div class="flex items-center gap-4">
        <a href="/room" class="text-black hover:text-gray-700 underline">← Quay lại</a>
        {#if $user}
          <div class="flex items-center gap-2">
            {#if $user.avatarUrl}
              <img src="{$user.avatarUrl}" alt="avatar" class="w-8 h-8 rounded-full border border-gray-300 bg-gray-100 object-cover" />
            {:else}
              <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 font-bold border border-gray-300">
                {($user.displayName || $user.email)?.[0]?.toUpperCase()}
              </div>
            {/if}
            <div class="flex flex-col leading-tight">
              <span class="text-sm font-semibold text-black">{$user.displayName || 'Không tên'}</span>
              <span class="text-xs text-gray-400">{$user.email}</span>
            </div>
          </div>
        {/if}
        <button class="text-sm text-black cursor-pointer underline hover:text-gray-700 cursor-pointer" on:click={handleLogout}>Đăng xuất</button>
      </div>
    </div>

    <!-- Create Room Form -->
    <form on:submit|preventDefault={createRoom} class="space-y-6">
      <!-- Room Type Selection -->
      <fieldset>
        <legend class="block text-sm font-medium text-black mb-2">Loại phòng</legend>
        <div class="flex gap-4">
          <label class="flex items-center">
            <input type="radio" bind:group={roomType} value="private" class="mr-2" />
            <span class="text-black">Phòng riêng tư (1-1)</span>
          </label>
          <label class="flex items-center">
            <input type="radio" bind:group={roomType} value="group" class="mr-2" />
            <span class="text-black">Phòng nhóm</span>
          </label>
        </div>
      </fieldset>

      <!-- Room Name -->
      <div>
        <label for="roomName" class="block text-sm font-medium text-black mb-2">Tên phòng</label>
        <input
          id="roomName"
          type="text"
          bind:value={roomName}
          class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-black transition"
          placeholder="Nhập tên phòng..."
          required
        />
      </div>

      <!-- Room Password -->
      <div>
        <label for="roomPassword" class="block text-sm font-medium text-black mb-2">Mật khẩu phòng</label>
        <div class="space-y-2">
          <PasswordInput
            id="roomPassword"
            bind:value={roomPassword}
            placeholder="Nhập mật khẩu..."
            showStrength={true}
            helperText="Mật khẩu phải có ít nhất 8 ký tự, chứa cả chữ và số, không được là mật khẩu phổ biến"
            className="w-full border border-gray-300 rounded-lg px-4 py-2 pr-12 focus:outline-none focus:ring-2 focus:ring-black transition bg-white text-black"
            required={true}
          />
          <button
            type="button"
            class="w-full px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors flex items-center justify-center gap-2"
            on:click={() => roomPassword = generateRandomPassword(12)}
            title="Tạo mật khẩu phòng mạnh ngẫu nhiên"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Tạo mật khẩu ngẫu nhiên
          </button>
        </div>
      </div>

      <!-- Friend Selection -->
      <div>
        <div class="block text-sm font-medium text-black mb-2">
          Chọn bạn bè
          {#if roomType === 'private'}
            (chọn đúng 1 người)
          {:else}
            (chọn ít nhất 1 người)
          {/if}
        </div>
        
        {#if $friendsLoading}
          <div class="text-center py-6">
            <div class="w-full max-w-sm mx-auto p-4 bg-gray-50 rounded-lg border border-gray-200">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-black mx-auto mb-2"></div>
              <p class="text-black font-medium text-sm">Đang tải danh sách bạn bè...</p>
            </div>
          </div>
        {:else if $friends.length === 0}
          <div class="text-gray-400 py-4">
            Chưa có bạn bè nào. 
            <a href="/friends" class="text-blue-600 hover:underline">Tìm kiếm và kết bạn</a>
          </div>
        {:else}
          <div class="max-h-48 overflow-y-auto border border-gray-200 rounded-lg p-4 space-y-2">
            {#each $friends as friend}
              <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                <input
                  type="checkbox"
                  checked={selectedFriends.includes(friend.$id)}
                  on:change={() => toggleFriendSelection(friend.$id)}
                  class="mr-3"
                />
                <div class="flex items-center gap-3 flex-1">
                  <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 font-bold text-sm">
                    {(friend.displayName || friend.email)?.[0]?.toUpperCase()}
                  </div>
                  <div>
                    <div class="font-medium text-black">{friend.displayName || 'Không tên'}</div>
                    <div class="text-sm text-gray-500">{friend.email}</div>
                  </div>
                </div>
              </label>
            {/each}
          </div>
          <p class="text-xs text-gray-500 mt-2">Đã chọn: {selectedFriends.length} người</p>
        {/if}
      </div>

      <!-- Error Message -->
      {#if $error}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {$error}
        </div>
      {/if}

      <!-- Submit Button -->
      <div class="flex gap-4">
        <button
          type="submit"
          disabled={$loading || $friends.length === 0}
          class="flex-1 py-3 bg-black text-white rounded-lg hover:bg-gray-900 transition font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {$loading ? 'Đang tạo...' : 'Tạo phòng'}
        </button>
        <a
          href="/room"
          class="px-6 py-3 border border-gray-300 text-black rounded-lg hover:bg-gray-50 transition font-medium text-center"
        >
          Hủy
        </a>
      </div>
    </form>
  </section>
</main>
