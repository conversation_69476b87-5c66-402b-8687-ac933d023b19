import { redirect } from '@sveltejs/kit';
import type { PageLoad } from './$types';
import { account } from '$lib/appwrite';

export const load: PageLoad = async () => {
  try {
    // Check if user is authenticated
    const user = await account.get();
    
    if (!user.emailVerification) {
      throw redirect(302, '/auth');
    }

    return {
      user: {
        id: user.$id,
        email: user.email,
        displayName: user.name,
        avatarUrl: user.prefs?.avatarUrl || '',
        emailVerified: user.emailVerification
      }
    };
  } catch (authError) {
    throw redirect(302, '/auth');
  }
};
