<script lang="ts">
import { onMount } from 'svelte';
import { user, isAuthenticated } from '$lib/stores/auth';
import { goto } from '$app/navigation';
import { writable, get } from 'svelte/store';
import { account, databases } from '$lib/appwrite';
import { Query } from 'appwrite';
import { ensureUserProfile } from '$lib/utils/userProfile';

// Database config
const databaseId = '686a3883000ac5847c3d';
const usersCollectionId = '686a3fc900213085a7b3'; // Collection để lưu user profiles
const friendRequestsCollectionId = '686a3f9e0032e366477a'; // Collection để lưu friend requests

// Search state
let searchQuery = '';
let searchResults = writable<any[]>([]);
let searchLoading = writable(false);
let searchError = writable('');

// Friends state
let friends = writable<any[]>([]);
let friendRequests = writable<any[]>([]);
let friendsLoading = writable(true);
let friendsError = writable('');

onMount(async () => {
  // Check authentication
  try {
    const acc = await account.get();
    const currentUser = {
      id: acc.$id,
      email: acc.email,
      displayName: acc.name,
      avatarUrl: acc.prefs?.avatarUrl || '',
      emailVerified: acc.emailVerification
    };
    user.set(currentUser);
    isAuthenticated.set(true);

    // Ensure user profile exists in database
    try {
      await ensureUserProfile(currentUser);
    } catch (profileErr) {
      console.log('Failed to ensure user profile:', profileErr);
    }
  } catch {
    user.set(null);
    isAuthenticated.set(false);
    goto('/auth');
    return;
  }

  // Load only friend requests (not auto-load friends)
  await loadFriendRequests();
});

async function searchUsers() {
  if (!searchQuery.trim()) {
    searchResults.set([]);
    return;
  }

  searchLoading.set(true);
  searchError.set('');
  
  try {
    let results: any[] = [];
    
    // Search by email (exact match)
    if (searchQuery.includes('@')) {
      try {
        const emailResults = await databases.listDocuments(databaseId, usersCollectionId, [
          Query.equal('email', searchQuery.toLowerCase())
        ]);
        results = [...results, ...emailResults.documents];
      } catch (err) {
        console.log('Email search failed:', err);
      }
    }
    
    // Search by displayName (full-text search)
    try {
      const nameResults = await databases.listDocuments(databaseId, usersCollectionId, [
        Query.search('displayName', searchQuery)
      ]);
      results = [...results, ...nameResults.documents];
    } catch (err) {
      console.log('Name search failed:', err);
    }
    
    // Remove duplicates and current user
    const currentUser = get(user);
    const uniqueResults = results.filter((searchUser, index, self) =>
      index === self.findIndex(u => u.$id === searchUser.$id) &&
      searchUser.$id !== currentUser?.id
    );
    
    searchResults.set(uniqueResults);
    
    if (uniqueResults.length === 0) {
      searchError.set('Không tìm thấy người dùng nào.');
    }
  } catch (err: any) {
    console.error('Search error:', err);
    if (err.code === 404) {
      searchError.set('Collection "users" chưa được tạo. Vui lòng tạo collection trong Appwrite Console.');
    } else {
      searchError.set(`Lỗi tìm kiếm: ${err.message}`);
    }
    searchResults.set([]);
  } finally {
    searchLoading.set(false);
  }
}

async function loadFriends() {
  const currentUser = get(user);
  if (!currentUser) return;
  
  friendsLoading.set(true);
  friendsError.set('');
  
  try {
    // Load accepted friend requests where current user is involved
    const sentRequests = await databases.listDocuments(databaseId, friendRequestsCollectionId, [
      Query.equal('from', currentUser.id),
      Query.equal('status', 'accepted')
    ]);

    const receivedRequests = await databases.listDocuments(databaseId, friendRequestsCollectionId, [
      Query.equal('to', currentUser.id),
      Query.equal('status', 'accepted')
    ]);
    
    // Get friend IDs
    const friendIds = [
      ...sentRequests.documents.map(req => req.to),
      ...receivedRequests.documents.map(req => req.from)
    ];
    
    // Load friend profiles
    const friendProfiles = [];
    for (const friendId of friendIds) {
      try {
        const profile = await databases.getDocument(databaseId, usersCollectionId, friendId);
        friendProfiles.push(profile);
      } catch (err) {
        console.log(`Failed to load profile for ${friendId}:`, err);
      }
    }
    
    friends.set(friendProfiles);
  } catch (err: any) {
    console.error('Error loading friends:', err);
    friendsError.set(`Không thể tải danh sách bạn bè: ${err.message}`);
    friends.set([]);
  } finally {
    friendsLoading.set(false);
  }
}

async function loadFriendRequests() {
  const currentUser = get(user);
  if (!currentUser) return;

  try {
    // Load pending friend requests sent to current user
    const pendingRequests = await databases.listDocuments(databaseId, friendRequestsCollectionId, [
      Query.equal('to', currentUser.id),
      Query.equal('status', 'pending')
    ]);
    
    // Load sender profiles
    const requestsWithProfiles = [];
    for (const request of pendingRequests.documents) {
      try {
        const senderProfile = await databases.getDocument(databaseId, usersCollectionId, request.from);
        requestsWithProfiles.push({
          ...request,
          senderProfile
        });
      } catch (err) {
        console.log(`Failed to load sender profile for request ${request.$id}:`, err);
      }
    }
    
    friendRequests.set(requestsWithProfiles);
  } catch (err: any) {
    console.error('Error loading friend requests:', err);
  }
}

async function sendFriendRequest(targetUserId: string) {
  const currentUser = get(user);
  if (!currentUser) return;

  try {
    // Check if request already exists
    const existingRequests = await databases.listDocuments(databaseId, friendRequestsCollectionId, [
      Query.equal('from', currentUser.id),
      Query.equal('to', targetUserId)
    ]);
    
    if (existingRequests.documents.length > 0) {
      alert('Bạn đã gửi lời mời kết bạn cho người này rồi.');
      return;
    }
    
    // Create friend request
    await databases.createDocument(databaseId, friendRequestsCollectionId, 'unique()', {
      from: currentUser.id,
      to: targetUserId,
      status: 'pending',
      createdAt: new Date().toISOString()
    });
    
    alert('Đã gửi lời mời kết bạn!');
    
    // Refresh search results to update button states
    await searchUsers();
  } catch (err: any) {
    console.error('Error sending friend request:', err);
    alert(`Không thể gửi lời mời: ${err.message}`);
  }
}

async function respondToFriendRequest(requestId: string, action: 'accept' | 'reject') {
  try {
    await databases.updateDocument(databaseId, friendRequestsCollectionId, requestId, {
      status: action === 'accept' ? 'accepted' : 'rejected'
    });
    
    // Reload friend requests and friends
    await loadFriendRequests();
    if (action === 'accept') {
      await loadFriends();
    }
    
    alert(action === 'accept' ? 'Đã chấp nhận lời mời kết bạn!' : 'Đã từ chối lời mời kết bạn.');
  } catch (err: any) {
    console.error('Error responding to friend request:', err);
    alert(`Không thể ${action === 'accept' ? 'chấp nhận' : 'từ chối'} lời mời: ${err.message}`);
  }
}

async function handleLogout() {
  try {
    await account.deleteSession('current');
  } catch {}
  user.set(null);
  isAuthenticated.set(false);
  goto('/auth');
}
</script>

<main class="flex flex-col items-center p-5 min-h-screen bg-gradient-to-br from-white to-gray-100">
  <section class="w-full max-w-4xl mx-auto mt-10 mb-8 p-8 bg-white rounded-2xl shadow-xl border border-gray-200 animate-fade-in">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
      <h1 class="text-3xl font-bold text-black">Tìm kiếm & Kết bạn</h1>
      <div class="flex items-center gap-4">
        <a href="/room" class="text-black hover:text-gray-700 underline">← Quay lại phòng</a>
        {#if $user}
          <div class="flex items-center gap-2">
            {#if $user.avatarUrl}
              <img src="{$user.avatarUrl}" alt="avatar" class="w-8 h-8 rounded-full border border-gray-300 bg-gray-100 object-cover" />
            {:else}
              <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 font-bold border border-gray-300">
                {($user.displayName || $user.email)?.[0]?.toUpperCase()}
              </div>
            {/if}
            <div class="flex flex-col leading-tight">
              <span class="text-sm font-semibold text-black">{$user.displayName || 'Không tên'}</span>
              <span class="text-xs text-gray-400">{$user.email}</span>
            </div>
          </div>
        {/if}
        <button class="text-sm text-black cursor-pointer underline hover:text-gray-700" on:click={handleLogout}>Đăng xuất</button>
      </div>
    </div>

    <!-- Search Section -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold mb-4">Tìm kiếm người dùng</h2>
      <div class="flex gap-4 mb-4">
        <input
          class="flex-1 border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-black transition"
          type="text"
          placeholder="Nhập email hoặc tên hiển thị..."
          bind:value={searchQuery}
          on:input={searchUsers}
        />
        <button 
          class="px-6 py-2 bg-black text-white rounded-lg hover:bg-gray-900 transition font-medium"
          on:click={searchUsers}
          disabled={$searchLoading}
        >
          {$searchLoading ? 'Đang tìm...' : 'Tìm kiếm'}
        </button>
      </div>
      
      {#if $searchError}
        <div class="text-red-500 text-sm mb-4">{$searchError}</div>
      {/if}
      
      {#if $searchResults.length > 0}
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          {#each $searchResults as searchUser}
            <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                  <div class="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 font-bold">
                    {(searchUser.displayName || searchUser.email)?.[0]?.toUpperCase()}
                  </div>
                  <div>
                    <div class="font-semibold text-black">{searchUser.displayName || 'Không tên'}</div>
                    <div class="text-sm text-gray-500">{searchUser.email}</div>
                  </div>
                </div>
                <button 
                  class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition text-sm font-medium"
                  on:click={() => sendFriendRequest(searchUser.$id)}
                >
                  Kết bạn
                </button>
              </div>
            </div>
          {/each}
        </div>
      {/if}
    </div>

    <!-- Friend Requests Section -->
    {#if $friendRequests.length > 0}
      <div class="mb-8">
        <h2 class="text-xl font-semibold mb-4">Lời mời kết bạn ({$friendRequests.length})</h2>
        <div class="space-y-4">
          {#each $friendRequests as request}
            <div class="border border-gray-200 rounded-lg p-4 bg-yellow-50">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                  <div class="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 font-bold">
                    {(request.senderProfile.displayName || request.senderProfile.email)?.[0]?.toUpperCase()}
                  </div>
                  <div>
                    <div class="font-semibold text-black">{request.senderProfile.displayName || 'Không tên'}</div>
                    <div class="text-sm text-gray-500">{request.senderProfile.email}</div>
                    <div class="text-xs text-gray-400">Gửi lời mời kết bạn</div>
                  </div>
                </div>
                <div class="flex gap-2">
                  <button 
                    class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition text-sm font-medium"
                    on:click={() => respondToFriendRequest(request.$id, 'accept')}
                  >
                    Chấp nhận
                  </button>
                  <button 
                    class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition text-sm font-medium"
                    on:click={() => respondToFriendRequest(request.$id, 'reject')}
                  >
                    Từ chối
                  </button>
                </div>
              </div>
            </div>
          {/each}
        </div>
      </div>
    {/if}

    <!-- Note about friends -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <h3 class="font-semibold text-blue-900 mb-2">💡 Lưu ý về bạn bè</h3>
      <p class="text-blue-800 text-sm">
        Để bảo vệ quyền riêng tư, danh sách bạn bè không được hiển thị công khai.
        Bạn chỉ có thể tìm kiếm người dùng cụ thể bằng email hoặc tên hiển thị.
      </p>
    </div>
  </section>
</main>
