import { error, redirect } from '@sveltejs/kit';
import type { PageLoad } from './$types';
import { account, databases } from '$lib/appwrite';

const databaseId = '686a3883000ac5847c3d';
const roomsCollectionId = '686a38b00033c187fde6';

export const load: PageLoad = async ({ params }) => {
  const { roomId } = params;

  try {
    // Check if user is authenticated
    const user = await account.get();
    
    if (!user.emailVerification) {
      throw redirect(302, '/auth');
    }

    // Check if room exists and user has access
    try {
      const room = await databases.getDocument(databaseId, roomsCollectionId, roomId);
      
      return {
        roomId,
        room: {
          id: room.$id,
          name: room.name,
          isGroup: room.isGroup || false,
          passwordProtected: room.passwordProtected || true
        },
        user: {
          id: user.$id,
          email: user.email,
          displayName: user.name,
          avatarUrl: user.prefs?.avatarUrl || '',
          emailVerified: user.emailVerification
        }
      };
    } catch (roomError) {
      throw error(404, 'Phòng chat không tồn tại hoặc bạn không có quyền truy cập');
    }
  } catch (authError) {
    throw redirect(302, '/auth');
  }
};
