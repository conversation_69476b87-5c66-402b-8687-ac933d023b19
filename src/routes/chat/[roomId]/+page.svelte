<script lang="ts">
import { onMount, onDestroy } from 'svelte';
import { page } from '$app/state';
import { user, isAuthenticated } from '$lib/stores/auth';
import { goto } from '$app/navigation';
import { writable } from 'svelte/store';
import { account, client, databases } from '$lib/appwrite';
import { ID, Query, type Models, type RealtimeResponseEvent } from 'appwrite';

// Database config
const databaseId = '686a3883000ac5847c3d';
const roomsCollectionId = '686a38b00033c187fde6';
const messagesCollectionId = 'messages';

// Room and messages state
let currentRoom = writable<{ id: string; name: string; isGroup: boolean } | null>(null);
let messages = writable<{ id: string; sender: string; text: string; time: string }[]>([]);
let messagesLoading = writable(false);
let messagesError = writable('');
let roomLoading = writable(true);
let roomError = writable('');

// Form state
let input = '';

// Realtime subscription
let unsubscribe: (() => void) | null = null;
let messagesContainer: HTMLElement;

// Get roomId from URL params
$: roomId = page.params.roomId;

onMount(async () => {
  // Check authentication
  try {
    const acc = await account.get();
    user.set({
      id: acc.$id,
      email: acc.email,
      displayName: acc.name,
      avatarUrl: acc.prefs?.avatarUrl || '',
      emailVerified: acc.emailVerification
    });
    isAuthenticated.set(true);
  } catch {
    user.set(null);
    isAuthenticated.set(false);
    goto('/auth');
    return;
  }

  // Load room data and messages
  if (roomId) {
    await loadRoom(roomId);
    await loadMessages(roomId);
    setupRealtimeSubscription(roomId);
  }
});

onDestroy(() => {
  if (unsubscribe) {
    unsubscribe();
  }
});

async function loadRoom(roomId: string) {
  roomLoading.set(true);
  roomError.set('');
  try {
    const room = await databases.getDocument(databaseId, roomsCollectionId, roomId);
    currentRoom.set({
      id: room.$id,
      name: room.name,
      isGroup: room.isGroup || false
    });
  } catch (err) {
    roomError.set('Không thể tải thông tin phòng.');
    currentRoom.set(null);
  } finally {
    roomLoading.set(false);
  }
}

async function loadMessages(roomId: string) {
  messagesLoading.set(true);
  messagesError.set('');
  try {
    const res = await databases.listDocuments(databaseId, messagesCollectionId, [
      Query.equal('roomId', roomId),
      Query.orderDesc('$createdAt'),
      Query.limit(50)
    ]);
    const fetched = res.documents.map((doc: any) => ({
      id: doc.$id,
      sender: doc.senderName,
      text: doc.text,
      time: new Date(doc.$createdAt).toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })
    })).reverse();
    messages.set(fetched);
  } catch (err) {
    messagesError.set('Không thể tải tin nhắn.');
    messages.set([]);
  } finally {
    messagesLoading.set(false);
  }
}

function setupRealtimeSubscription(roomId: string) {
  if (unsubscribe) unsubscribe();
  
  unsubscribe = client.subscribe(
    [`databases.${databaseId}.collections.${messagesCollectionId}.documents`],
    (event: RealtimeResponseEvent<Models.Document>) => {
      if (event.events.some((e: string) => e.includes('databases.*.collections.*.documents.*.create')) && 
          event.payload.roomId === roomId) {
        messages.update((msgs) => [
          ...msgs,
          {
            id: event.payload.$id,
            sender: event.payload.senderName,
            text: event.payload.text,
            time: new Date(event.payload.$createdAt).toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })
          }
        ]);
        // Auto scroll to bottom when new message arrives
        setTimeout(() => scrollToBottom(), 100);
      }
    }
  );
}

async function sendMessage() {
  if (!input.trim() || !$user || !roomId) return;

  const senderName = $user.displayName || $user.email || 'Bạn';
  try {
    await databases.createDocument(databaseId, messagesCollectionId, ID.unique(), {
      roomId,
      senderName,
      text: input
    });
    input = '';
    // Auto scroll to bottom after sending
    setTimeout(() => scrollToBottom(), 100);
  } catch (err) {
    console.error('Error sending message:', err);
  }
}

function scrollToBottom() {
  if (messagesContainer) {
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
  }
}

async function handleLogout() {
  try {
    await account.deleteSession('current');
  } catch {}
  user.set(null);
  isAuthenticated.set(false);
  goto('/auth');
}

function goBackToRooms() {
  goto('/room');
}
</script>

<main class="flex flex-col min-h-screen bg-gradient-to-br from-white to-gray-100">
  <!-- Mobile-first responsive chat container -->
  <div class="flex flex-col h-screen max-w-6xl mx-auto w-full bg-white shadow-xl md:rounded-2xl md:my-4 md:h-[calc(100vh-2rem)] overflow-hidden chat-container animate-fade-in">
    <!-- Header -->
    <header class="flex items-center justify-between px-4 py-3 md:px-6 md:py-4 border-b border-gray-200 bg-white sticky top-0 z-10">
      <div class="flex items-center gap-3 flex-1 min-w-0">
        <button
          class="p-2 -ml-2 text-black hover:bg-gray-100 rounded-full transition-colors md:hidden"
          on:click={goBackToRooms}
          aria-label="Quay lại danh sách phòng"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
        </button>
        <button
          class="hidden md:flex items-center gap-2 text-black hover:text-gray-700 transition-colors"
          on:click={goBackToRooms}
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
          <span>Quay lại</span>
        </button>

        {#if $roomLoading}
          <div class="flex items-center gap-2">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"></div>
            <span class="text-gray-600 text-sm">Đang tải...</span>
          </div>
        {:else if $roomError}
          <div class="text-red-500 text-sm truncate">{$roomError}</div>
        {:else if $currentRoom}
          <div class="min-w-0 flex-1">
            <h1 class="text-lg md:text-xl font-bold text-black truncate">{$currentRoom.name}</h1>
            <div class="flex items-center gap-2">
              <span class="text-xs md:text-sm text-gray-500">
                {$currentRoom.isGroup ? 'Nhóm chat' : 'Chat 1-1'}
              </span>
              <div class="w-2 h-2 bg-green-500 rounded-full"></div>
              <span class="text-xs text-gray-400">Online</span>
            </div>
          </div>
        {/if}
      </div>

      <!-- User info and actions -->
      <div class="flex items-center gap-2 md:gap-4">
        {#if $user}
          <div class="hidden md:flex items-center gap-2">
            {#if $user.avatarUrl}
              <img src="{$user.avatarUrl}" alt="avatar" class="w-8 h-8 rounded-full border border-gray-300 bg-gray-100 object-cover" />
            {:else}
              <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 font-bold border border-gray-300 text-sm">
                {($user.displayName || $user.email)?.[0]?.toUpperCase()}
              </div>
            {/if}
            <div class="flex flex-col leading-tight">
              <span class="text-sm font-semibold text-black">{$user.displayName || 'Không tên'}</span>
              <span class="text-xs text-gray-400">{$user.email}</span>
            </div>
          </div>
          <!-- Mobile user avatar -->
          <div class="md:hidden">
            {#if $user.avatarUrl}
              <img src="{$user.avatarUrl}" alt="avatar" class="w-8 h-8 rounded-full border border-gray-300 bg-gray-100 object-cover" />
            {:else}
              <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 font-bold border border-gray-300 text-sm">
                {($user.displayName || $user.email)?.[0]?.toUpperCase()}
              </div>
            {/if}
          </div>
        {/if}
        <!-- Logout button -->
        <button
          class="p-2 text-black hover:bg-gray-100 rounded-full transition-colors cursor-pointer"
          on:click={handleLogout}
          aria-label="Đăng xuất"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
          </svg>
        </button>
      </div>
    </header>

    <!-- Messages Area -->
    <div
      bind:this={messagesContainer}
      class="flex-1 overflow-y-auto px-4 py-4 md:px-6 bg-gray-50 md:bg-white messages-container"
    >
      {#if $messagesLoading}
        <div class="flex justify-center items-center h-32">
          <div class="text-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-black mx-auto mb-2"></div>
            <p class="text-black font-medium text-sm">Đang tải tin nhắn...</p>
          </div>
        </div>
      {:else if $messagesError}
        <div class="flex justify-center items-center h-32">
          <div class="text-red-500 text-center text-sm">{$messagesError}</div>
        </div>
      {:else if $messages.length === 0}
        <div class="flex flex-col justify-center items-center h-32 text-center">
          <div class="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mb-4">
            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-black mb-2">Chưa có tin nhắn nào</h3>
          <p class="text-gray-500 text-sm">Hãy bắt đầu cuộc trò chuyện!</p>
        </div>
      {:else}
        <div class="space-y-4 pb-4">
          {#each $messages as msg, index}
            {@const isCurrentUser = msg.sender === ($user?.displayName || $user?.email)}
            {@const showAvatar = index === 0 || $messages[index - 1].sender !== msg.sender}

            <div class="flex {isCurrentUser ? 'justify-end' : 'justify-start'} items-end gap-2 {isCurrentUser ? 'animate-slide-in-right' : 'animate-slide-in-left'}">
              {#if !isCurrentUser && showAvatar}
                <div class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center text-gray-600 font-bold text-sm flex-shrink-0">
                  {msg.sender[0]?.toUpperCase()}
                </div>
              {:else if !isCurrentUser}
                <div class="w-8 h-8 flex-shrink-0"></div>
              {/if}

              <div class="max-w-xs md:max-w-md lg:max-w-lg">
                {#if !isCurrentUser && showAvatar}
                  <div class="text-xs text-gray-500 mb-1 ml-3">{msg.sender}</div>
                {/if}

                <div class="relative group">
                  <div class="px-4 py-2 rounded-2xl {isCurrentUser
                    ? 'bg-black text-white rounded-br-md'
                    : 'bg-white border border-gray-200 text-black rounded-bl-md'} shadow-sm">
                    <p class="text-sm md:text-base leading-relaxed">{msg.text}</p>
                  </div>

                  <div class="text-xs text-gray-400 mt-1 {isCurrentUser ? 'text-right' : 'text-left'} opacity-0 group-hover:opacity-100 transition-opacity">
                    {msg.time}
                  </div>
                </div>
              </div>

              {#if isCurrentUser && showAvatar}
                <div class="w-8 h-8 rounded-full bg-black flex items-center justify-center text-white font-bold text-sm flex-shrink-0">
                  {($user?.displayName || $user?.email)?.[0]?.toUpperCase()}
                </div>
              {:else if isCurrentUser}
                <div class="w-8 h-8 flex-shrink-0"></div>
              {/if}
            </div>
          {/each}
        </div>
      {/if}
    </div>

    <!-- Message Input -->
    <div class="border-t border-gray-200 bg-white p-4">
      <form class="flex gap-3 items-end" on:submit|preventDefault={sendMessage}>
        <div class="flex-1">
          <div class="relative">
            <input
              class="w-full border border-gray-300 rounded-2xl px-4 py-3 pr-12 focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition bg-white text-black placeholder-gray-500 text-sm md:text-base focus-ring"
              type="text"
              placeholder="Nhập tin nhắn..."
              bind:value={input}
              autocomplete="off"
            />
            <!-- Emoji button placeholder -->
            <button
              type="button"
              class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
              aria-label="Chọn emoji"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </button>
          </div>
        </div>

        <button
          type="submit"
          disabled={!input.trim()}
          class="p-3 rounded-full bg-black hover:bg-gray-900 text-white font-semibold transition disabled:opacity-50 disabled:cursor-not-allowed flex-shrink-0"
          aria-label="Gửi tin nhắn"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
          </svg>
        </button>
      </form>
    </div>
  </div>
</main>
