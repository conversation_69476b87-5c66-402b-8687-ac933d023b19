<script lang="ts">
import { onMount, onDestroy } from 'svelte';
import { page } from '$app/stores';
import { user, isAuthenticated } from '$lib/stores/auth';
import { goto } from '$app/navigation';
import { writable } from 'svelte/store';
import { account, client, databases } from '$lib/appwrite';
import { ID, Query, type Models, type RealtimeResponseEvent } from 'appwrite';

// Database config
const databaseId = '686a3883000ac5847c3d';
const roomsCollectionId = '686a38b00033c187fde6';
const messagesCollectionId = 'messages';

// Room and messages state
let currentRoom = writable<{ id: string; name: string; isGroup: boolean } | null>(null);
let messages = writable<{ id: string; sender: string; text: string; time: string }[]>([]);
let messagesLoading = writable(false);
let messagesError = writable('');
let roomLoading = writable(true);
let roomError = writable('');

// Form state
let input = '';

// Realtime subscription
let unsubscribe: (() => void) | null = null;

// Get roomId from URL params
$: roomId = $page.params.roomId;

onMount(async () => {
  // Check authentication
  try {
    const acc = await account.get();
    user.set({
      id: acc.$id,
      email: acc.email,
      displayName: acc.name,
      avatarUrl: acc.prefs?.avatarUrl || '',
      emailVerified: acc.emailVerification
    });
    isAuthenticated.set(true);
  } catch {
    user.set(null);
    isAuthenticated.set(false);
    goto('/auth');
    return;
  }

  // Load room data and messages
  if (roomId) {
    await loadRoom(roomId);
    await loadMessages(roomId);
    setupRealtimeSubscription(roomId);
  }
});

onDestroy(() => {
  if (unsubscribe) {
    unsubscribe();
  }
});

async function loadRoom(roomId: string) {
  roomLoading.set(true);
  roomError.set('');
  try {
    const room = await databases.getDocument(databaseId, roomsCollectionId, roomId);
    currentRoom.set({
      id: room.$id,
      name: room.name,
      isGroup: room.isGroup || false
    });
  } catch (err) {
    roomError.set('Không thể tải thông tin phòng.');
    currentRoom.set(null);
  } finally {
    roomLoading.set(false);
  }
}

async function loadMessages(roomId: string) {
  messagesLoading.set(true);
  messagesError.set('');
  try {
    const res = await databases.listDocuments(databaseId, messagesCollectionId, [
      Query.equal('roomId', roomId),
      Query.orderDesc('$createdAt'),
      Query.limit(50)
    ]);
    const fetched = res.documents.map((doc: any) => ({
      id: doc.$id,
      sender: doc.senderName,
      text: doc.text,
      time: new Date(doc.$createdAt).toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })
    })).reverse();
    messages.set(fetched);
  } catch (err) {
    messagesError.set('Không thể tải tin nhắn.');
    messages.set([]);
  } finally {
    messagesLoading.set(false);
  }
}

function setupRealtimeSubscription(roomId: string) {
  if (unsubscribe) unsubscribe();
  
  unsubscribe = client.subscribe(
    [`databases.${databaseId}.collections.${messagesCollectionId}.documents`],
    (event: RealtimeResponseEvent<Models.Document>) => {
      if (event.events.some((e: string) => e.includes('databases.*.collections.*.documents.*.create')) && 
          event.payload.roomId === roomId) {
        messages.update((msgs) => [
          ...msgs,
          {
            id: event.payload.$id,
            sender: event.payload.senderName,
            text: event.payload.text,
            time: new Date(event.payload.$createdAt).toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })
          }
        ]);
      }
    }
  );
}

async function sendMessage() {
  if (!input.trim() || !$user || !roomId) return;
  
  const senderName = $user.displayName || $user.email || 'Bạn';
  try {
    await databases.createDocument(databaseId, messagesCollectionId, ID.unique(), {
      roomId,
      senderName,
      text: input
    });
    input = '';
  } catch (err) {
    console.error('Error sending message:', err);
  }
}

async function handleLogout() {
  try {
    await account.deleteSession('current');
  } catch {}
  user.set(null);
  isAuthenticated.set(false);
  goto('/auth');
}

function goBackToRooms() {
  goto('/room');
}
</script>

<main class="flex flex-col items-center p-5 min-h-screen bg-gradient-to-br from-white to-gray-100">
  <section class="w-full max-w-4xl mx-auto mt-10 mb-8 p-0 bg-white rounded-2xl shadow-xl border border-gray-200 animate-fade-in flex flex-col overflow-hidden">
    <!-- Header -->
    <div class="flex items-center justify-between px-6 py-4 border-b border-gray-200">
      <div class="flex items-center gap-4">
        <button 
          class="text-black hover:text-gray-700 transition"
          on:click={goBackToRooms}
        >
          ← Quay lại
        </button>
        {#if $roomLoading}
          <div class="text-gray-400">Đang tải...</div>
        {:else if $roomError}
          <div class="text-red-500">{$roomError}</div>
        {:else if $currentRoom}
          <div>
            <h3 class="text-xl font-bold text-black">{$currentRoom.name}</h3>
            <span class="text-sm text-gray-500">
              {$currentRoom.isGroup ? 'Nhóm chat' : 'Chat 1-1'}
            </span>
          </div>
        {/if}
      </div>
      
      <div class="flex items-center gap-4">
        {#if $user}
          <div class="flex items-center gap-2">
            {#if $user.avatarUrl}
              <img src="{$user.avatarUrl}" alt="avatar" class="w-8 h-8 rounded-full border border-gray-300 bg-gray-100 object-cover" />
            {:else}
              <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 font-bold border border-gray-300">
                {($user.displayName || $user.email)?.[0]?.toUpperCase()}
              </div>
            {/if}
            <div class="flex flex-col leading-tight">
              <span class="text-sm font-semibold text-black">{$user.displayName || 'Không tên'}</span>
              <span class="text-xs text-gray-400">{$user.email}</span>
            </div>
          </div>
        {/if}
        <button class="text-sm text-black cursor-pointer underline hover:text-gray-700" on:click={handleLogout}>Đăng xuất</button>
      </div>
    </div>

    <!-- Messages Area -->
    <div class="flex-1 overflow-y-auto px-6 py-4 bg-white min-h-96 max-h-96">
      {#if $messagesLoading}
        <div class="text-gray-400 text-center mt-10">Đang tải tin nhắn...</div>
      {:else if $messagesError}
        <div class="text-red-500 text-center mt-10">{$messagesError}</div>
      {:else if $messages.length === 0}
        <div class="text-gray-400 text-center mt-10">Chưa có tin nhắn nào. Hãy bắt đầu cuộc trò chuyện!</div>
      {/if}
      
      {#each $messages as msg}
        <div class="mb-3">
          <span class="font-semibold text-black">{msg.sender}</span>
          <span class="mx-2 text-gray-400 text-xs">{msg.time}</span>
          <div class="ml-2 text-black">{msg.text}</div>
        </div>
      {/each}
    </div>

    <!-- Message Input -->
    <form class="flex gap-2 border-t border-gray-200 p-4 bg-white" on:submit|preventDefault={sendMessage}>
      <input
        class="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black transition bg-white text-black"
        type="text"
        placeholder="Nhập tin nhắn..."
        bind:value={input}
        autocomplete="off"
      />
      <button class="px-5 py-2 rounded-lg bg-black hover:bg-gray-900 text-white font-semibold transition" type="submit">
        Gửi
      </button>
    </form>
  </section>
</main>
