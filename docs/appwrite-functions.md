# Appwrite Functions Setup Guide

## Overview

Theo base.prompt.md, tất cả permission handling ph<PERSON>i đ<PERSON><PERSON><PERSON> thực hiện server-side thông qua Appwrite Functions để đảm bảo bảo mật. Client SDK không đượ<PERSON> phép set hoặc modify permissions.

## Required Functions

### 1. Room Password Validation Function

**Function Name:** `validate-room-password`
**Runtime:** Node.js 18
**Trigger:** HTTP Request

**Purpose:** Validate room password và grant access permissions

**Code Example:**

```javascript
const { Client, Databases, Users } = require('node-appwrite');

module.exports = async ({ req, res, log, error }) => {
  const client = new Client()
    .setEndpoint(process.env.APPWRITE_FUNCTION_ENDPOINT)
    .setProject(process.env.APPWRITE_FUNCTION_PROJECT_ID)
    .setKey(process.env.APPWRITE_API_KEY);

  const databases = new Databases(client);
  const users = new Users(client);

  try {
    const { roomId, password, userId } = JSON.parse(req.body);

    if (!roomId || !password || !userId) {
      return res.json({ 
        success: false, 
        error: 'Missing required parameters' 
      }, 400);
    }

    // Get room document
    const room = await databases.getDocument(
      process.env.DATABASE_ID,
      process.env.ROOMS_COLLECTION_ID,
      roomId
    );

    // Validate password
    if (room.password !== password) {
      return res.json({ 
        success: false, 
        error: 'Invalid password' 
      }, 401);
    }

    // Grant read permission to user
    const updatedRoom = await databases.updateDocument(
      process.env.DATABASE_ID,
      process.env.ROOMS_COLLECTION_ID,
      roomId,
      {},
      [
        ...room.$permissions,
        `read("user:${userId}")`,
        `update("user:${userId}")`
      ]
    );

    // Add user to room members if not already added
    if (!room.members.includes(userId)) {
      await databases.updateDocument(
        process.env.DATABASE_ID,
        process.env.ROOMS_COLLECTION_ID,
        roomId,
        {
          members: [...room.members, userId]
        }
      );
    }

    return res.json({ 
      success: true, 
      message: 'Access granted',
      room: updatedRoom
    });

  } catch (err) {
    error('Function error: ' + err.message);
    return res.json({ 
      success: false, 
      error: err.message 
    }, 500);
  }
};
```

### 2. Message Permission Function

**Function Name:** `grant-message-permissions`
**Runtime:** Node.js 18
**Trigger:** Database Event (Messages Collection)

**Purpose:** Automatically grant read permissions for messages to room members

**Code Example:**

```javascript
const { Client, Databases } = require('node-appwrite');

module.exports = async ({ req, res, log, error }) => {
  const client = new Client()
    .setEndpoint(process.env.APPWRITE_FUNCTION_ENDPOINT)
    .setProject(process.env.APPWRITE_FUNCTION_PROJECT_ID)
    .setKey(process.env.APPWRITE_API_KEY);

  const databases = new Databases(client);

  try {
    const { events, payload } = req;

    // Only handle create events
    if (!events.some(event => event.includes('create'))) {
      return res.json({ success: true, message: 'Not a create event' });
    }

    const messageId = payload.$id;
    const roomId = payload.roomId;

    // Get room to find members
    const room = await databases.getDocument(
      process.env.DATABASE_ID,
      process.env.ROOMS_COLLECTION_ID,
      roomId
    );

    // Grant read permissions to all room members
    const permissions = room.members.map(memberId => `read("user:${memberId}")`);

    await databases.updateDocument(
      process.env.DATABASE_ID,
      process.env.MESSAGES_COLLECTION_ID,
      messageId,
      {},
      [
        ...payload.$permissions,
        ...permissions
      ]
    );

    return res.json({ 
      success: true, 
      message: 'Permissions granted to room members' 
    });

  } catch (err) {
    error('Function error: ' + err.message);
    return res.json({ 
      success: false, 
      error: err.message 
    }, 500);
  }
};
```

## Environment Variables

Cần set các environment variables sau trong Appwrite Functions:

```
APPWRITE_FUNCTION_ENDPOINT=https://nyc.cloud.appwrite.io/v1
APPWRITE_FUNCTION_PROJECT_ID=68671f7800356b227e15
APPWRITE_API_KEY=<your-api-key>
DATABASE_ID=686a3883000ac5847c3d
ROOMS_COLLECTION_ID=686a38b00033c187fde6
MESSAGES_COLLECTION_ID=messages
USERS_COLLECTION_ID=users
FRIEND_REQUESTS_COLLECTION_ID=friend_requests
```

## Setup Instructions

1. **Tạo API Key:**
   - Vào Appwrite Console > Settings > API Keys
   - Tạo API Key với scopes: `databases.read`, `databases.write`, `users.read`

2. **Tạo Functions:**
   - Vào Appwrite Console > Functions
   - Tạo function mới với runtime Node.js 18
   - Upload code và set environment variables

3. **Configure Triggers:**
   - HTTP trigger cho `validate-room-password`
   - Database trigger cho `grant-message-permissions` (Messages collection, create events)

4. **Update Client Code:**
   - Thay thế direct database calls bằng function calls
   - Sử dụng Functions.createExecution() để call functions

## Client Integration

```typescript
import { Functions } from 'appwrite';

const functions = new Functions(client);

// Validate room password
async function joinRoomWithPassword(roomId: string, password: string) {
  try {
    const result = await functions.createExecution(
      'validate-room-password',
      JSON.stringify({ roomId, password, userId: user.id })
    );
    
    const response = JSON.parse(result.response);
    if (response.success) {
      // Access granted, redirect to room
      goto(`/chat/${roomId}`);
    } else {
      // Show error
      passwordError = response.error;
    }
  } catch (err) {
    console.error('Function execution failed:', err);
  }
}
```

## Security Benefits

1. **Password Security:** Passwords không bao giờ được expose ở client-side
2. **Permission Control:** Chỉ server mới có thể modify permissions
3. **Access Control:** Validate user access trước khi grant permissions
4. **Audit Trail:** Tất cả permission changes được log ở server-side

## Next Steps

1. Implement các functions trên trong Appwrite Console
2. Update client code để sử dụng functions thay vì direct database calls
3. Test thoroughly để đảm bảo security
4. Add logging và monitoring cho functions
